<?php
// 简单的API测试脚本
header('Content-Type: application/json');

// 测试数据
$testData = [
    'username' => 'testuser',
    'email' => '<EMAIL>',
    'password' => 'password123',
    'invite_code' => 'INVITE2025'
];

// 发送POST请求到注册API
$url = 'http://localhost:8000/api/v1/auth/register';
$options = [
    'http' => [
        'header' => "Content-Type: application/json\r\n",
        'method' => 'POST',
        'content' => json_encode($testData)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo json_encode(['error' => 'Failed to call API']);
} else {
    echo $result;
}
?>

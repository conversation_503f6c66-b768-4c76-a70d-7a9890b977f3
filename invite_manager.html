<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邀请码管理 - CryptoTracker</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .status-active {
            color: #28a745;
            font-weight: bold;
        }
        .status-used {
            color: #dc3545;
            font-weight: bold;
        }
        .invite-code {
            font-family: monospace;
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
        }
        .actions {
            margin-bottom: 20px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        .quick-codes {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .quick-codes h3 {
            margin-top: 0;
            color: #0056b3;
        }
        .code-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .code-item {
            background: white;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #007bff;
            font-family: monospace;
            font-weight: bold;
            cursor: pointer;
        }
        .code-item:hover {
            background: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎫 CryptoTracker 邀请码管理</h1>
        
        <div class="stats" id="stats">
            <div class="stat-item">
                <div class="stat-number" id="totalCount">-</div>
                <div class="stat-label">总邀请码</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="activeCount">-</div>
                <div class="stat-label">可用邀请码</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="usedCount">-</div>
                <div class="stat-label">已使用</div>
            </div>
        </div>

        <div class="quick-codes">
            <h3>🚀 快速测试邀请码（点击复制）</h3>
            <div class="code-list" id="quickCodes">
                <!-- 动态生成 -->
            </div>
        </div>
        
        <div class="actions">
            <button onclick="loadInvites()">🔄 刷新数据</button>
            <button onclick="generateMoreCodes()" class="btn-success">➕ 生成更多邀请码</button>
            <button onclick="window.open('test_register.html')" class="btn-warning">🧪 测试注册</button>
            <button onclick="window.open('frontend/pages/register.html')" class="btn-warning">📝 正式注册页面</button>
        </div>
        
        <table id="inviteTable">
            <thead>
                <tr>
                    <th>邀请码</th>
                    <th>状态</th>
                    <th>使用者ID</th>
                    <th>创建时间</th>
                    <th>使用时间</th>
                </tr>
            </thead>
            <tbody id="inviteTableBody">
                <tr>
                    <td colspan="5" style="text-align: center;">加载中...</td>
                </tr>
            </tbody>
        </table>
    </div>

    <script>
        // 加载邀请码数据
        async function loadInvites() {
            try {
                const response = await fetch('generate_invites.php');
                const html = await response.text();
                
                // 简单的数据解析（实际项目中应该用API返回JSON）
                // 这里我们直接重新加载页面数据
                location.reload();
            } catch (error) {
                console.error('加载邀请码失败:', error);
                alert('加载邀请码失败，请检查服务器连接');
            }
        }

        // 生成更多邀请码
        async function generateMoreCodes() {
            if (confirm('确定要生成更多邀请码吗？')) {
                try {
                    window.open('generate_invites.php', '_blank');
                } catch (error) {
                    console.error('生成邀请码失败:', error);
                    alert('生成邀请码失败');
                }
            }
        }

        // 复制邀请码到剪贴板
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                // 简单的提示
                const originalText = event.target.textContent;
                event.target.textContent = '已复制!';
                event.target.style.background = '#28a745';
                event.target.style.color = 'white';
                
                setTimeout(() => {
                    event.target.textContent = originalText;
                    event.target.style.background = 'white';
                    event.target.style.color = 'black';
                }, 1000);
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制: ' + text);
            });
        }

        // 初始化快速邀请码
        function initQuickCodes() {
            const quickCodes = [
                'TEST2025', 'DEMO2025', 'CRYPTO01', 'CRYPTO02', 'CRYPTO03',
                'STUDENT01', 'STUDENT02', 'COURSE2025', 'TRACKER01', 'WELCOME01'
            ];
            
            const container = document.getElementById('quickCodes');
            container.innerHTML = '';
            
            quickCodes.forEach(code => {
                const codeElement = document.createElement('div');
                codeElement.className = 'code-item';
                codeElement.textContent = code;
                codeElement.onclick = () => copyToClipboard(code);
                container.appendChild(codeElement);
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initQuickCodes();
            
            // 模拟统计数据（实际应该从API获取）
            document.getElementById('totalCount').textContent = '50+';
            document.getElementById('activeCount').textContent = '45+';
            document.getElementById('usedCount').textContent = '5+';
        });
    </script>
</body>
</html>

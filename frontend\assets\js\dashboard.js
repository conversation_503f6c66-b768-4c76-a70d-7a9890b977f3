// 格式化数字显示（添加千位分隔符和保留小数位）
function formatNumber(number, decimals = 2) {
    if (number === null || number === undefined) return '0';
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(number);
}

// 格式化价格显示
function formatPrice(price) {
    if (price >= 1) {
        return '$' + formatNumber(price, 2);
    } else {
        return '$' + formatNumber(price, 6);
    }
}

// 格式化市值显示（K, M, B, T）
function formatMarketCap(marketCap) {
    if (marketCap >= 1e12) {
        return '$' + formatNumber(marketCap / 1e12, 2) + 'T';
    } else if (marketCap >= 1e9) {
        return '$' + formatNumber(marketCap / 1e9, 2) + 'B';
    } else if (marketCap >= 1e6) {
        return '$' + formatNumber(marketCap / 1e6, 2) + 'M';
    } else if (marketCap >= 1e3) {
        return '$' + formatNumber(marketCap / 1e3, 2) + 'K';
    } else {
        return '$' + formatNumber(marketCap, 2);
    }
}

// 获取币种图标路径
function getCoinIconPath(symbol) {
    if (!symbol) return '';
    return `../assets/img/coins/${symbol.toLowerCase()}.png`;
}

// 格式化数字为货币格式
function formatCurrency(value) {
    if (value >= 1e9) {
        return '$' + (value / 1e9).toFixed(2) + 'B';
    } else if (value >= 1e6) {
        return '$' + (value / 1e6).toFixed(2) + 'M';
    } else {
        return '$' + value.toFixed(2);
    }
}

// 格式化百分比
function formatPercentage(value) {
    const formattedValue = parseFloat(value).toFixed(2);
    return formattedValue > 0 ? `+${formattedValue}%` : `${formattedValue}%`;
}

// 格式化变化百分比
function formatChangePercent(value) {
    if (value === null || value === undefined) return '0%';
    const sign = value >= 0 ? '+' : '';
    return `${sign}${value.toFixed(2)}%`;
}

// 更新热门币种区域
function updateHotCoins(hotCoins) {
    const container = document.querySelector('.crypto-hot-coins .row');
    if (!container) return;

    container.innerHTML = hotCoins.map(coin => `
        <div class="col-md-3">
            <div class="crypto-coin-card">
                <div class="d-flex align-items-center">
                    <img src="${coin.image_url}" alt="${coin.symbol}" class="crypto-coin-icon me-2">
                    <div>
                        <h6 class="text-white mb-1">${coin.symbol}</h6>
                        <span class="${coin.price_change_24h >= 0 ? 'text-success' : 'text-danger'}">
                            ${coin.price_change_24h >= 0 ? '+' : ''}${formatNumber(coin.price_change_24h)}%
                        </span>
                    </div>
                </div>
                <div class="text-end">
                    <h5 class="text-white mb-1">${formatPrice(coin.price)}</h5>
                </div>
            </div>
        </div>
    `).join('');
}

// 更新市场统计数据
function updateMarketStats(stats) {
    // 更新总市值
    const marketCapElement = document.querySelector('.crypto-stats-grid .crypto-stat-card:nth-child(1) h3');
    if (marketCapElement) {
        marketCapElement.textContent = formatMarketCap(stats.total_market_cap);
    }

    // 更新24h交易量
    const volumeElement = document.querySelector('.crypto-stats-grid .crypto-stat-card:nth-child(2) h3');
    if (volumeElement) {
        volumeElement.textContent = formatMarketCap(stats.total_volume_24h);
    }

    // 更新比特币主导地位
    const btcDomElement = document.querySelector('.crypto-stats-grid .crypto-stat-card:nth-child(3) h3');
    if (btcDomElement) {
        btcDomElement.textContent = formatNumber(stats.btc_dominance, 1) + '%';

        // 更新变化百分比
        const btcDomChangeElement = btcDomElement.parentElement.querySelector('.crypto-stat-change');
        if (btcDomChangeElement) {
            const change = stats.btc_dominance_change;
            btcDomChangeElement.className = 'crypto-stat-change ' + (change >= 0 ? 'crypto-price-up' : 'crypto-price-down');
            btcDomChangeElement.innerHTML = `
                <i class="bi bi-caret-${change >= 0 ? 'up' : 'down'}-fill"></i>
                <span>${Math.abs(change).toFixed(1)}%</span>
            `;
        }
    }
}

// 更新市场列表数据
function updateMarketList(marketList) {
    const tbody = document.querySelector('.crypto-market-table tbody');
    if (!tbody) return;

    tbody.innerHTML = marketList.map(coin => `
        <tr>
            <td>
                <div class="d-flex align-items-center">
                    <span class="text-white">${coin.symbol}</span>
                </div>
            </td>
            <td class="text-white">${formatPrice(coin.price_usd)}</td>
            <td class="${coin.price_change_percent_24h >= 0 ? 'text-success' : 'text-danger'}">
                ${coin.price_change_percent_24h >= 0 ? '+' : ''}${formatNumber(coin.price_change_percent_24h)}%
            </td>
            <td class="text-white">
                <div>高: ${formatPrice(coin.high_24h)}</div>
                <div>低: ${formatPrice(coin.low_24h)}</div>
            </td>
            <td class="text-white">${formatMarketCap(coin.volume_24h_usd)}</td>
            <td class="text-white">${formatMarketCap(coin.market_cap_usd)}</td>
            <td class="text-white">${formatNumber(coin.market_share_percentage, 2)}%</td>
        </tr>
    `).join('');
}

// 更新最新上市数据
function updateNewListings(newListings) {
    const container = document.querySelector('.crypto-card:nth-child(1) .crypto-list');
    if (!container) return;

    container.innerHTML = newListings.map(coin => `
        <div class="crypto-list-item">
            <div class="d-flex align-items-center">
                <img src="${coin.image_url}" alt="${coin.symbol}" class="crypto-coin-icon me-2">
                <div>
                    <h6 class="text-white mb-1">${coin.symbol}</h6>
                    <span class="${coin.price_change_24h >= 0 ? 'text-success' : 'text-danger'}">
                        ${coin.price_change_24h >= 0 ? '+' : ''}${formatNumber(coin.price_change_24h)}%
                    </span>
                </div>
            </div>
            <div class="text-end">
                <h6 class="text-white mb-1">${formatPrice(coin.price)}</h6>
            </div>
        </div>
    `).join('');
}

// 更新最大涨幅数据
function updateTopGainers(topGainers) {
    const container = document.querySelector('.crypto-card:nth-child(2) .crypto-list');
    if (!container) return;

    container.innerHTML = topGainers.map(coin => `
        <div class="crypto-list-item">
            <div class="d-flex align-items-center">
                <img src="${coin.image_url}" alt="${coin.symbol}" class="crypto-coin-icon me-2">
                <div>
                    <h6 class="text-white mb-1">${coin.symbol}</h6>
                    <span class="text-success">
                        +${formatNumber(coin.price_change_24h)}%
                    </span>
                </div>
            </div>
            <div class="text-end">
                <h6 class="text-white mb-1">${formatPrice(coin.price)}</h6>
            </div>
        </div>
    `).join('');
}

// 更新所有数据
async function updateAllData() {
    try {
        const response = await fetch(CryptoApp.apiBaseUrl + '/dashboard/overview', {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + Auth.getToken()
            }
        });
        const result = await response.json();

        if (result.status === 'success') {
            const data = result.data;

            // 更新各个区域的数据
            updateMarketStats(data);

            // 获取热门币种数据
            const hotCoinsResponse = await fetch(CryptoApp.apiBaseUrl + '/dashboard/hot-coins', {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + Auth.getToken()
                }
            });
            const hotCoinsResult = await hotCoinsResponse.json();
            if (hotCoinsResult.status === 'success') {
                updateHotCoins(hotCoinsResult.data);
            }

            // 获取最新上市数据
            const newListingsResponse = await fetch(CryptoApp.apiBaseUrl + '/dashboard/new-listings', {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + Auth.getToken()
                }
            });
            const newListingsResult = await newListingsResponse.json();
            if (newListingsResult.status === 'success') {
                updateNewListings(newListingsResult.data);
            }

            // 获取涨幅榜数据
            const topGainersResponse = await fetch(CryptoApp.apiBaseUrl + '/dashboard/top-gainers', {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + Auth.getToken()
                }
            });
            const topGainersResult = await topGainersResponse.json();
            if (topGainersResult.status === 'success') {
                updateTopGainers(topGainersResult.data);
            }

            // 更新最后更新时间
            const lastUpdateTime = document.querySelector('.crypto-welcome-content p');
            if (lastUpdateTime) {
                lastUpdateTime.textContent = '最后更新: ' + new Date().toLocaleString();
            }
        } else {
            console.error('获取数据失败:', result.message);
            CryptoApp.toast.error('获取数据失败，请刷新重试');
        }
    } catch (error) {
        console.error('更新数据时出错:', error);
        CryptoApp.toast.error('更新数据失败，请检查网络连接');
    }
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 绑定刷新按钮事件
    const refreshButton = document.getElementById('refreshData');
    if (refreshButton) {
        refreshButton.addEventListener('click', updateAllData);
    }

    // 注意：不在这里设置定时器，避免重复
});

// 仪表盘页面的主要功能
const Dashboard = {
    formatChangePercent: formatChangePercent,

    // 获取市场概览数据
    getMarketOverview: async function() {
        try {
            const response = await fetch(`${CryptoApp.apiBaseUrl}/dashboard/overview`, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + Auth.getToken()
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.status === 'success') {
                const overview = data.data;

                // 更新总市值
                const marketCapCard = document.querySelector('.crypto-stat-card:nth-child(1)');
                if (marketCapCard) {
                    const h3 = marketCapCard.querySelector('h3');
                    const change = marketCapCard.querySelector('.crypto-stat-change');
                    if (h3) h3.textContent = formatMarketCap(overview.total_market_cap);
                    if (change) {
                        const changeValue = overview.market_cap_change_24h;
                        change.className = `crypto-stat-change ${changeValue >= 0 ? 'crypto-price-up' : 'crypto-price-down'}`;
                        change.innerHTML = `
                            <i class="bi bi-caret-${changeValue >= 0 ? 'up' : 'down'}-fill"></i>
                            <span>${Math.abs(changeValue).toFixed(2)}%</span>
                        `;
                    }
                }

                // 更新24h交易量
                const volumeCard = document.querySelector('.crypto-stat-card:nth-child(2)');
                if (volumeCard) {
                    const h3 = volumeCard.querySelector('h3');
                    const change = volumeCard.querySelector('.crypto-stat-change');
                    if (h3) h3.textContent = formatMarketCap(overview.total_volume_24h);
                    if (change) change.innerHTML = this.formatChangePercent(overview.volume_change_24h);
                }

                // 更新BTC占比
                const btcCard = document.querySelector('.crypto-stat-card:nth-child(3)');
                if (btcCard) {
                    const h3 = btcCard.querySelector('h3');
                    const change = btcCard.querySelector('.crypto-stat-change');
                    if (h3) h3.textContent = overview.btc_dominance.toFixed(1) + '%';
                    if (change) change.innerHTML = this.formatChangePercent(overview.btc_dominance_change_24h);
                }

                // 更新最后更新时间
                const lastUpdateTime = document.querySelector('.crypto-welcome-content p');
                if (lastUpdateTime) {
                    lastUpdateTime.textContent = '最后更新: ' + new Date(overview.last_updated).toLocaleString();
                }
            }
        } catch (error) {
            console.error('加载市场概览失败:', error);
            // 显示错误信息给用户
            const welcomeContent = document.querySelector('.crypto-welcome-content p');
            if (welcomeContent) {
                welcomeContent.textContent = '数据加载失败，请刷新重试';
            }
        }
    },

    // 获取涨幅榜数据
    getTopGainers: async function() {
        try {
            const response = await fetch(CryptoApp.apiBaseUrl + '/market/top-gainers', {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + Auth.getToken()
                }
            });
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('获取涨幅榜数据失败:', error);
            return null;
        }
    },

    // 获取跌幅榜数据
    getTopLosers: async function() {
        try {
            const response = await fetch(CryptoApp.apiBaseUrl + '/market/top-losers', {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + Auth.getToken()
                }
            });
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('获取跌幅榜数据失败:', error);
            return null;
        }
    },

    // 获取交易量榜数据
    getTopVolume: async function() {
        try {
            const response = await fetch(CryptoApp.apiBaseUrl + '/market/volume', {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + Auth.getToken()
                }
            });
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('获取交易量榜数据失败:', error);
            return null;
        }
    },

    // 更新市场概览UI
    updateMarketOverview: function(data) {
        if (!data) return;

        // 更新总市值
        document.querySelector('.total-market-cap').textContent = CryptoApp.utils.formatCurrency(data.total_market_cap);
        document.querySelector('.market-cap-change').textContent = CryptoApp.utils.formatPercent(data.market_cap_change_24h);

        // 更新24h成交量
        document.querySelector('.total-volume').textContent = CryptoApp.utils.formatCurrency(data.total_volume_24h);
        document.querySelector('.volume-change').textContent = CryptoApp.utils.formatPercent(data.volume_change_24h);

        // 更新比特币占比
        document.querySelector('.btc-dominance').textContent = CryptoApp.utils.formatPercent(data.btc_dominance);
        document.querySelector('.dominance-change').textContent = CryptoApp.utils.formatPercent(data.btc_dominance_change);
    },

    // 更新排行榜UI
    updateRankings: function(gainers, losers, volume) {
        // 更新涨幅榜
        const gainersTable = document.querySelector('#topGainersTable tbody');
        if (gainersTable) {
            gainersTable.innerHTML = this.generateRankingRows(gainers);
        }

        // 更新跌幅榜
        const losersTable = document.querySelector('#topLosersTable tbody');
        if (losersTable) {
            losersTable.innerHTML = this.generateRankingRows(losers);
        }

        // 更新交易量榜
        const volumeTable = document.querySelector('#topVolumeTable tbody');
        if (volumeTable) {
            volumeTable.innerHTML = this.generateRankingRows(volume, true);
        }
    },

    // 生成排行榜行HTML
    generateRankingRows: function(data, isVolume = false) {
        if (!Array.isArray(data)) return '';

        return data.map((item, index) => `
            <tr>
                <td>${index + 1}</td>
                <td>
                    <img src="${item.image}" alt="${item.symbol}" class="crypto-icon">
                    ${item.name} (${item.symbol.toUpperCase()})
                </td>
                <td>${CryptoApp.utils.formatCurrency(item.price)}</td>
                <td class="${item.price_change_24h >= 0 ? 'text-success' : 'text-danger'}">
                    ${CryptoApp.utils.formatPercent(item.price_change_24h)}
                </td>
                ${isVolume ? `<td>${CryptoApp.utils.formatCurrency(item.volume_24h)}</td>` : ''}
            </tr>
        `).join('');
    },

    // 初始化仪表盘
    init: async function() {
        try {
            // 获取并更新市场概览
            await this.getMarketOverview();

            // 获取并更新排行榜数据
            const [gainers, losers, volume] = await Promise.all([
                this.getTopGainers(),
                this.getTopLosers(),
                this.getTopVolume()
            ]);

            this.updateRankings(gainers, losers, volume);

            // 设置定时刷新（只设置一个定时器，避免重复）
            setInterval(async () => {
                console.log('定时刷新数据...');
                await this.getMarketOverview();

                const [newGainers, newLosers, newVolume] = await Promise.all([
                    this.getTopGainers(),
                    this.getTopLosers(),
                    this.getTopVolume()
                ]);

                this.updateRankings(newGainers, newLosers, newVolume);
            }, 120000); // 改为2分钟刷新一次，减少频率

        } catch (error) {
            console.error('初始化仪表盘失败:', error);
            CryptoApp.toast.error('加载数据失败，请刷新重试');
        }
    }
};

// 页面加载完成后初始化仪表盘（只保留一个初始化）
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否已经初始化过，避免重复初始化
    if (!window.dashboardInitialized) {
        window.dashboardInitialized = true;
        console.log('初始化仪表盘...');
        Dashboard.init();
    }
});

// 当前页码和每页显示数量
let currentPage = 1;
let pageSize = 10;

// 获取市场表格数据
async function fetchMarketTableData() {
    try {
        const response = await fetch(`${CryptoApp.apiBaseUrl}/dashboard/market-table?page=${currentPage}&limit=${pageSize}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + Auth.getToken()
            }
        });
        const result = await response.json();

        if (result.status === 'success') {
            updateMarketTable(result.data);
        } else {
            console.error('Failed to fetch market table data:', result.message);
        }
    } catch (error) {
        console.error('Error fetching market table data:', error);
    }
}

// 更新市场表格
function updateMarketTable(data) {
    const tbody = document.querySelector('.crypto-market-table tbody');
    const totalRecordsSpan = document.getElementById('totalRecords');
    const paginationContainer = document.getElementById('marketTablePagination');

    if (!tbody || !totalRecordsSpan || !paginationContainer) return;

    // 更新表格内容
    tbody.innerHTML = data.data.map(coin => `
        <tr>
            <td>
                <div class="d-flex align-items-center">
                    <img src="${coin.image_url || '../assets/img/coins/generic.png'}" alt="${coin.symbol}" class="crypto-coin-icon me-2" onerror="this.src='../assets/img/coins/generic.png'">
                    <div>
                        <div class="text-white">${coin.symbol.toUpperCase()}</div>
                        <small class="text-secondary">${coin.name || coin.symbol}</small>
                    </div>
                </div>
            </td>
            <td class="text-white">${formatPrice(coin.price)}</td>
            <td class="${coin.price_change_24h >= 0 ? 'text-success' : 'text-danger'}">
                ${coin.price_change_24h >= 0 ? '+' : ''}${formatNumber(coin.price_change_24h, 2)}%
            </td>
            <td>
                <div class="text-success">H: ${formatPrice(coin.high_24h)}</div>
                <div class="text-danger">L: ${formatPrice(coin.low_24h)}</div>
            </td>
            <td class="text-white">${formatMarketCap(coin.volume_24h)}</td>
            <td class="text-white">${formatMarketCap(coin.market_cap)}</td>
            <td class="text-white">${formatNumber(coin.market_share_percentage, 2)}%</td>
            <td class="text-white">${new Date(coin.last_updated).toLocaleString()}</td>
        </tr>
    `).join('');

    // 更新总记录数
    totalRecordsSpan.textContent = data.total;

    // 更新分页
    const totalPages = Math.ceil(data.total / data.limit);
    updatePagination(totalPages);
}

// 更新分页控件
function updatePagination(totalPages) {
    const paginationContainer = document.getElementById('marketTablePagination');
    if (!paginationContainer) return;

    let paginationHTML = '';

    // 上一页按钮
    paginationHTML += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${currentPage - 1}">上一页</a>
        </li>
    `;

    // 页码按钮
    for (let i = 1; i <= totalPages; i++) {
        if (
            i === 1 || // 第一页
            i === totalPages || // 最后一页
            (i >= currentPage - 1 && i <= currentPage + 1) // 当前页的前后一页
        ) {
            paginationHTML += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `;
        } else if (
            i === currentPage - 2 ||
            i === currentPage + 2
        ) {
            paginationHTML += `
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            `;
        }
    }

    // 下一页按钮
    paginationHTML += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${currentPage + 1}">下一页</a>
        </li>
    `;

    paginationContainer.innerHTML = paginationHTML;

    // 添加分页事件监听
    paginationContainer.querySelectorAll('.page-link').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const newPage = parseInt(e.target.dataset.page);
            if (!isNaN(newPage) && newPage !== currentPage) {
                currentPage = newPage;
                fetchMarketTableData();
            }
        });
    });
}

// 初始化表格
function initMarketTable() {
    // 监听每页显示数量变化
    const limitSelect = document.getElementById('marketTableLimit');
    if (limitSelect) {
        limitSelect.addEventListener('change', (e) => {
            pageSize = parseInt(e.target.value);
            currentPage = 1; // 重置到第一页
            fetchMarketTableData();
        });
    }

    // 首次加载数据
    fetchMarketTableData();
}

// 在页面加载完成后初始化表格
document.addEventListener('DOMContentLoaded', () => {
    initMarketTable();
});

// 市场数据表格功能
class MarketDataTable {
    constructor() {
        this.table = document.getElementById('marketDataTable');
        this.limitSelect = document.getElementById('marketTableLimit');
        this.tbody = this.table.querySelector('tbody');
        this.currentSort = { column: 'market_cap', direction: 'desc' };
        this.currentPage = 1;
        this.pageSize = 10;
        this.apiBaseUrl = CryptoApp.apiBaseUrl;

        this.init();
    }

    init() {
        // 初始化排序事件
        this.table.querySelectorAll('th.sortable').forEach(th => {
            th.addEventListener('click', () => this.handleSort(th));
        });

        // 初始化每页显示数量变化事件
        if (this.limitSelect) {
            this.limitSelect.addEventListener('change', () => {
                this.pageSize = parseInt(this.limitSelect.value);
                this.currentPage = 1; // 重置到第一页
                this.fetchData();
            });
        }

        // 加载初始数据
        this.fetchData();
    }

    async fetchData() {
        try {
            // 构建查询参数
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                sort_field: this.currentSort.column,
                sort_order: this.currentSort.direction
            });

            // 发送请求到后端API
            const response = await fetch(`${this.apiBaseUrl}/dashboard/market-table?${params}`, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + Auth.getToken()
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.status === 'success') {
                this.renderTable(result.data);
            } else {
                throw new Error(result.message || '获取数据失败');
            }
        } catch (error) {
            console.error('Failed to fetch market data:', error);
            this.showError('获取数据失败，请稍后重试');
        }
    }

    renderTable(data) {
        if (!data || !data.data || data.data.length === 0) {
            this.showEmptyState();
            return;
        }

        // 渲染表格内容
        this.tbody.innerHTML = data.data.map(item => `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <img src="${item.image_url}"
                             alt="${item.symbol}"
                             class="crypto-coin-icon me-2"
                             onerror="this.src='../assets/img/coins/generic.png'">
                        <div>
                            <div class="text-white">${item.symbol.toUpperCase()}</div>
                            <small class="text-secondary">${item.name}</small>
                        </div>
                    </div>
                </td>
                <td class="text-white text-end">${this.formatPrice(item.price)}</td>
                <td class="text-end">
                    <span class="${item.price_change_24h >= 0 ? 'text-success' : 'text-danger'}">
                        ${item.price_change_24h >= 0 ? '+' : ''}${item.price_change_24h.toFixed(2)}%
                    </span>
                </td>
                <td class="text-end">
                    <div class="text-success">H: ${this.formatPrice(item.high_24h)}</div>
                    <div class="text-danger">L: ${this.formatPrice(item.low_24h)}</div>
                </td>
                <td class="text-white text-end">${this.formatVolume(item.volume_24h)}</td>
                <td class="text-white text-end">${this.formatMarketCap(item.market_cap)}</td>
                <td class="text-white text-end">${item.market_share_percentage.toFixed(2)}%</td>
            </tr>
        `).join('');

        // 更新总记录数
        document.getElementById('totalRecords').textContent = data.total;

        // 更新分页
        this.updatePagination(Math.ceil(data.total / data.limit));
    }

    formatPrice(price) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: price >= 1 ? 2 : 6
        }).format(price);
    }

    formatVolume(volume) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            notation: 'compact',
            maximumFractionDigits: 2
        }).format(volume);
    }

    formatMarketCap(marketCap) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            notation: 'compact',
            maximumFractionDigits: 2
        }).format(marketCap);
    }

    showEmptyState() {
        this.tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4">
                    <div class="text-muted">暂无数据</div>
                </td>
            </tr>
        `;
        document.getElementById('totalRecords').textContent = '0';
        this.updatePagination(0);
    }

    showError(message) {
        this.tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4">
                    <div class="text-danger">${message}</div>
                </td>
            </tr>
        `;
    }

    handleSort(th) {
        const column = th.dataset.sort;
        // 如果点击的是当前排序列，则切换排序方向
        if (this.currentSort.column === column) {
            this.currentSort.direction = this.currentSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
            // 如果点击的是新列，则设置为默认降序
            this.currentSort = { column, direction: 'desc' };
        }

        // 重新获取数据
        this.fetchData();
    }

    updatePagination(totalPages) {
        const paginationContainer = document.getElementById('marketTablePagination');
        if (!paginationContainer) return;

        let paginationHTML = '';

        // 上一页按钮
        paginationHTML += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${this.currentPage - 1}">上一页</a>
            </li>
        `;

        // 页码按钮
        for (let i = 1; i <= totalPages; i++) {
            if (
                i === 1 || // 第一页
                i === totalPages || // 最后一页
                (i >= this.currentPage - 1 && i <= this.currentPage + 1) // 当前页的前后一页
            ) {
                paginationHTML += `
                    <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" data-page="${i}">${i}</a>
                    </li>
                `;
            } else if (
                i === this.currentPage - 2 ||
                i === this.currentPage + 2
            ) {
                paginationHTML += `
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                `;
            }
        }

        // 下一页按钮
        paginationHTML += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${this.currentPage + 1}">下一页</a>
            </li>
        `;

        paginationContainer.innerHTML = paginationHTML;

        // 添加分页事件监听
        paginationContainer.querySelectorAll('.page-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const newPage = parseInt(e.target.dataset.page);
                if (!isNaN(newPage) && newPage !== this.currentPage && newPage > 0 && newPage <= totalPages) {
                    this.currentPage = newPage;
                    this.fetchData();
                }
            });
        });
    }
}

// 初始化表格
document.addEventListener('DOMContentLoaded', () => {
    window.marketDataTable = new MarketDataTable();
});

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 加载导航栏
    loadNavbar();

    // 初始化页面数据
    initDashboard();
});

// 初始化仪表盘
async function initDashboard() {
    try {
        // 加载用户名
        const userInfo = JSON.parse(localStorage.getItem('user'));
        if (userInfo) {
            document.getElementById('username').textContent = userInfo.username;
        }

        // 加载币种列表
        await loadCoinList();

        // 初始化事件监听
        initEventListeners();

        // 初始化图表
        initCharts();
    } catch (error) {
        console.error('初始化仪表盘失败:', error);
        showError('加载数据失败，请稍后重试');
    }
}

// 加载币种列表
async function loadCoinList() {
    try {
        const response = await fetch('/api/v1/coins/list');
        const data = await response.json();

        if (data.success) {
            const select = document.getElementById('coin-select');
            select.innerHTML = '<option value="">选择币种...</option>';

            data.coins.forEach(coin => {
                const option = document.createElement('option');
                option.value = coin.symbol;
                option.textContent = `${coin.name} (${coin.symbol})`;
                select.appendChild(option);
            });
        } else {
            throw new Error(data.message || '加载币种列表失败');
        }
    } catch (error) {
        console.error('加载币种列表失败:', error);
        showError('加载币种列表失败');
    }
}

// 初始化事件监听
function initEventListeners() {
    // 币种选择变化事件
    document.getElementById('coin-select').addEventListener('change', async function() {
        const symbol = this.value;
        if (symbol) {
            await loadCoinData(symbol);
        } else {
            resetCharts();
        }
    });

    // 时间间隔切换事件
    document.querySelectorAll('[data-interval]').forEach(button => {
        button.addEventListener('click', async function() {
            document.querySelector('[data-interval].active').classList.remove('active');
            this.classList.add('active');

            const symbol = document.getElementById('coin-select').value;
            if (symbol) {
                await loadCoinData(symbol, this.dataset.interval);
            }
        });
    });

    // 刷新按钮点击事件
    document.getElementById('refresh-btn').addEventListener('click', async function() {
        this.disabled = true;
        try {
            const symbol = document.getElementById('coin-select').value;
            if (symbol) {
                await loadCoinData(symbol);
            }
            showSuccess('数据已更新');
        } catch (error) {
            showError('刷新数据失败');
        } finally {
            this.disabled = false;
        }
    });
}

// 加载币种数据
async function loadCoinData(symbol, interval = '1h') {
    try {
        // 显示加载状态
        showLoading(true);

        // 获取K线数据
        const klineResponse = await fetch(`/api/v1/coins/${symbol}/kline?interval=${interval}`);
        const klineData = await klineResponse.json();

        if (klineData.success) {
            // 更新K线图
            updateCandlestickChart(klineData.klines);

            // 更新交易量图
            updateVolumeChart(klineData.klines);

            // 更新技术指标
            updateIndicators(klineData.indicators);

            // 获取市场深度数据
            const depthResponse = await fetch(`/api/v1/coins/${symbol}/depth`);
            const depthData = await depthResponse.json();

            if (depthData.success) {
                // 更新深度图
                updateDepthChart(depthData.depth);
            }

            // 更新当前价格和涨跌幅
            document.getElementById('current-price').textContent = formatCurrency(klineData.current_price);
            const priceChange = document.getElementById('price-change');
            priceChange.textContent = formatPercentage(klineData.price_change_24h);
            priceChange.className = `h5 mb-0 ${klineData.price_change_24h >= 0 ? 'text-success' : 'text-danger'}`;
        } else {
            throw new Error(klineData.message || '加载K线数据失败');
        }
    } catch (error) {
        console.error('加载币种数据失败:', error);
        showError('加载币种数据失败');
    } finally {
        // 隐藏加载状态
        showLoading(false);
    }
}

// 初始化图表
function initCharts() {
    // 初始化K线图
    window.candlestickChart = new ApexCharts(document.querySelector("#candlestick-chart"), {
        series: [{
            data: []
        }],
        chart: {
            type: 'candlestick',
            height: 400,
            background: 'transparent',
            toolbar: {
                show: true,
                tools: {
                    download: false,
                    selection: true,
                    zoom: true,
                    zoomin: true,
                    zoomout: true,
                    pan: true,
                    reset: true
                }
            }
        },
        title: {
            text: 'K线图',
            align: 'left',
            style: {
                color: '#E2E8F0'
            }
        },
        xaxis: {
            type: 'datetime',
            labels: {
                style: {
                    colors: '#718096'
                }
            }
        },
        yaxis: {
            labels: {
                style: {
                    colors: '#718096'
                },
                formatter: function(value) {
                    return '$' + value.toFixed(2);
                }
            }
        },
        grid: {
            borderColor: '#2D3748'
        },
        plotOptions: {
            candlestick: {
                colors: {
                    upward: '#48BB78',
                    downward: '#F56565'
                }
            }
        }
    });
    window.candlestickChart.render();

    // 初始化交易量图
    window.volumeChart = new ApexCharts(document.querySelector("#volume-chart"), {
        series: [{
            name: '成交量',
            data: []
        }],
        chart: {
            type: 'bar',
            height: 250,
            background: 'transparent',
            toolbar: {
                show: false
            }
        },
        colors: ['#4299E1'],
        xaxis: {
            type: 'datetime',
            labels: {
                style: {
                    colors: '#718096'
                }
            }
        },
        yaxis: {
            labels: {
                style: {
                    colors: '#718096'
                },
                formatter: function(value) {
                    return formatNumber(value);
                }
            }
        },
        grid: {
            borderColor: '#2D3748'
        }
    });
    window.volumeChart.render();

    // 初始化深度图
    window.depthChart = new ApexCharts(document.querySelector("#depth-chart"), {
        series: [{
            name: '买单',
            data: []
        }, {
            name: '卖单',
            data: []
        }],
        chart: {
            type: 'area',
            height: 300,
            background: 'transparent',
            toolbar: {
                show: false
            }
        },
        colors: ['#48BB78', '#F56565'],
        stroke: {
            curve: 'smooth',
            width: 2
        },
        fill: {
            type: 'gradient',
            gradient: {
                opacityFrom: 0.4,
                opacityTo: 0.1
            }
        },
        xaxis: {
            type: 'numeric',
            labels: {
                style: {
                    colors: '#718096'
                },
                formatter: function(value) {
                    return '$' + value.toFixed(2);
                }
            }
        },
        yaxis: {
            labels: {
                style: {
                    colors: '#718096'
                },
                formatter: function(value) {
                    return formatNumber(value);
                }
            }
        },
        grid: {
            borderColor: '#2D3748'
        },
        legend: {
            labels: {
                colors: '#718096'
            }
        }
    });
    window.depthChart.render();
}

// 更新K线图
function updateCandlestickChart(klines) {
    const data = klines.map(k => ({
        x: new Date(k.time),
        y: [k.open, k.high, k.low, k.close]
    }));

    window.candlestickChart.updateSeries([{
        data: data
    }]);
}

// 更新交易量图
function updateVolumeChart(klines) {
    const data = klines.map(k => ({
        x: new Date(k.time),
        y: k.volume
    }));

    window.volumeChart.updateSeries([{
        data: data
    }]);
}

// 更新深度图
function updateDepthChart(depth) {
    window.depthChart.updateSeries([
        {
            name: '买单',
            data: depth.bids.map(b => ({
                x: parseFloat(b[0]),
                y: parseFloat(b[1])
            }))
        },
        {
            name: '卖单',
            data: depth.asks.map(a => ({
                x: parseFloat(a[0]),
                y: parseFloat(a[1])
            }))
        }
    ]);
}

// 更新技术指标
function updateIndicators(indicators) {
    document.getElementById('ma7').textContent = formatCurrency(indicators.ma7);
    document.getElementById('ma25').textContent = formatCurrency(indicators.ma25);
    document.getElementById('ma99').textContent = formatCurrency(indicators.ma99);
    document.getElementById('rsi').textContent = formatNumber(indicators.rsi);
    document.getElementById('macd').textContent = formatNumber(indicators.macd);
}

// 重置图表
function resetCharts() {
    window.candlestickChart.updateSeries([{ data: [] }]);
    window.volumeChart.updateSeries([{ data: [] }]);
    window.depthChart.updateSeries([{ data: [] }, { data: [] }]);

    document.getElementById('current-price').textContent = '--';
    document.getElementById('price-change').textContent = '--';
    document.getElementById('ma7').textContent = '--';
    document.getElementById('ma25').textContent = '--';
    document.getElementById('ma99').textContent = '--';
    document.getElementById('rsi').textContent = '--';
    document.getElementById('macd').textContent = '--';
}

// 显示/隐藏加载状态
function showLoading(show) {
    const charts = document.querySelectorAll('.card');
    charts.forEach(chart => {
        chart.classList.toggle('loading', show);
    });
}

// 显示成功提示
function showSuccess(message) {
    // TODO: 实现成功提示
    console.log('成功:', message);
}

// 显示错误提示
function showError(message) {
    // TODO: 实现错误提示
    console.error('错误:', message);
}
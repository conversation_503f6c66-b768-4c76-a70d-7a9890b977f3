<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试注册功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .invite-codes {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CryptoTracker 注册功能测试</h1>
        
        <div class="invite-codes">
            <h3>可用邀请码：</h3>
            <p><strong>INVITE2025</strong> 或 <strong>ALPHAENTRY</strong></p>
            <p><small>注意：每个邀请码只能使用一次</small></p>
        </div>
        
        <form id="registerForm">
            <div class="form-group">
                <label for="username">用户名：</label>
                <input type="text" id="username" name="username" placeholder="至少3个字符" required>
            </div>
            
            <div class="form-group">
                <label for="email">邮箱：</label>
                <input type="email" id="email" name="email" placeholder="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码：</label>
                <input type="password" id="password" name="password" placeholder="至少6个字符" required>
            </div>
            
            <div class="form-group">
                <label for="invite_code">邀请码：</label>
                <input type="text" id="invite_code" name="invite_code" placeholder="输入邀请码" required>
            </div>
            
            <button type="submit">注册账户</button>
        </form>
        
        <div id="result" class="result"></div>
        
        <div style="margin-top: 30px;">
            <h3>测试建议：</h3>
            <ol>
                <li>先测试无效邀请码（如：INVALID）</li>
                <li>再测试有效邀请码（INVITE2025 或 ALPHAENTRY）</li>
                <li>测试重复使用已用邀请码</li>
                <li>测试各种验证规则（用户名长度、邮箱格式等）</li>
            </ol>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api/v1';
        
        function showResult(message, isSuccess = true) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.style.display = 'block';
        }
        
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const data = {
                username: formData.get('username'),
                email: formData.get('email'),
                password: formData.get('password'),
                invite_code: formData.get('invite_code')
            };
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                console.log('注册响应:', result);
                
                if (result.status === 'success') {
                    showResult(`注册成功！用户ID: ${result.data.user_id}, 用户名: ${result.data.username}`, true);
                    // 清空表单
                    e.target.reset();
                } else {
                    showResult(`注册失败: ${result.message}`, false);
                }
            } catch (error) {
                showResult(`注册错误: ${error.message}`, false);
            }
        });
    </script>
</body>
</html>

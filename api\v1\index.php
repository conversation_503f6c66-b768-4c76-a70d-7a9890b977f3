<?php
// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0); // 关闭错误显示，我们会自己处理错误

// 设置错误处理函数
function handleError($errno, $errstr, $errfile, $errline) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Internal Server Error',
        'debug' => DEBUG ? [
            'error' => $errstr,
            'file' => $errfile,
            'line' => $errline
        ] : null
    ]);
    exit;
}

// 设置异常处理函数
function handleException($e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Internal Server Error',
        'debug' => DEBUG ? [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ] : null
    ]);
    exit;
}

// 注册错误和异常处理函数
set_error_handler('handleError');
set_exception_handler('handleException');

// 定义是否为调试模式
define('DEBUG', true);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 设置响应头
header('Content-Type: application/json');

// 加载必要的类
require_once __DIR__ . '/controllers/UserController.php';
require_once __DIR__ . '/controllers/MarketController.php';
require_once __DIR__ . '/controllers/DashboardController.php';
require_once __DIR__ . '/models/CryptocurrencyModel.php';
require_once __DIR__ . '/models/MarketSnapshotModel.php';
require_once __DIR__ . '/utils/response.php';

// 处理跨域请求
$allowed_origins = array(
    "http://127.0.0.1:5500",
    "http://localhost:5500",
    "http://127.0.0.1:8000",
    "http://localhost:8000"
);

$origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';

if (in_array($origin, $allowed_origins)) {
    header("Access-Control-Allow-Origin: " . $origin);
}

header("Access-Control-Allow-Methods: POST, GET, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");
header("Access-Control-Allow-Credentials: true");

// 如果是 OPTIONS 请求，直接返回
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // 获取请求路径
    $request_uri = $_SERVER['REQUEST_URI'];
    $base_path = '/api/v1';

    // 移除查询字符串
    $request_uri = parse_url($request_uri, PHP_URL_PATH);

    // 移除基础路径，获取实际的路由路径
    $path = substr($request_uri, strlen($base_path));
    $path = trim($path, '/');

    // 如果路径为空，返回 404
    if (empty($path)) {
        throw new Exception('API endpoint not found');
    }

    // 根据路径路由到对应的控制器
    switch ($path) {
        // 用户相关
        case 'auth/login':
            $controller = new Controllers\UserController();
            $controller->login();
            break;

        case 'auth/register':
            $controller = new Controllers\UserController();
            $controller->register();
            break;

        case 'auth/logout':
            $controller = new Controllers\UserController();
            $controller->logout();
            break;

        case 'auth/current-user':
            $controller = new Controllers\UserController();
            $controller->getCurrentUser();
            break;

        case 'auth/validate-invite':
            $controller = new Controllers\UserController();
            $controller->validateInvite();
            break;

        // 仪表盘相关
        case 'dashboard/overview':
            $controller = new Controllers\DashboardController();
            $controller->getMarketOverview();
            break;

        case 'dashboard/hot-coins':
            $controller = new Controllers\DashboardController();
            $controller->getHotCoins();
            break;

        case 'dashboard/new-listings':
            $controller = new Controllers\DashboardController();
            $controller->getNewListings();
            break;

        case 'dashboard/top-gainers':
            $controller = new Controllers\DashboardController();
            $controller->getTopGainers();
            break;

        case 'dashboard/market-table':
            $controller = new Controllers\DashboardController();
            $controller->getMarketTable();
            break;

        // 市场数据相关路由
        case 'market/stats':
            $controller = new Controllers\MarketController();
            $controller->getMarketStats();
            break;

        case 'market/details':
            $controller = new Controllers\MarketController();
            $controller->getMarketDetails();
            break;

        case 'market/trends':
            $controller = new Controllers\MarketController();
            $controller->getMarketTrends();
            break;

        case 'market/hot-coins':
            $controller = new Controllers\MarketController();
            $controller->getHotCoins();
            break;

        case 'market/list':
            $controller = new Controllers\MarketController();
            $controller->getMarketList();
            break;

        case 'market/new-listings':
            $controller = new Controllers\MarketController();
            $controller->getNewListings();
            break;

        case 'market/top-gainers':
            $controller = new Controllers\MarketController();
            $controller->getTopGainers();
            break;

        case 'market/top-losers':
            $controller = new Controllers\MarketController();
            $controller->getTopLosers();
            break;

        case 'market/volume':
            $controller = new Controllers\MarketController();
            $controller->getTopVolume();
            break;

        default:
            throw new Exception('API endpoint not found');
    }
} catch (Exception $e) {
    http_response_code(404);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage(),
        'debug' => DEBUG ? [
            'path' => $path ?? null,
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ] : null
    ]);
}
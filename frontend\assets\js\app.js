/**
 * CryptoTracker 全局应用脚本
 * 包含共享函数和工具方法
 */

// 全局命名空间
const CryptoApp = {
    // API基础路径
    apiBaseUrl: 'http://localhost:8000/api/v1',

    // 应用配置
    config: {
        apiUrl: '/api', // 生产环境中使用实际API URL
        useMockData: false, // 不再使用模拟数据
        refreshInterval: 60000, // 数据刷新间隔(毫秒)
        theme: 'dark', // 默认主题
        paths: {
            login: '/frontend/pages/login.html',
            register: '/frontend/pages/register.html',
            dashboard: '/frontend/pages/dashboard.html'
        }
    },

    // 用户信息
    user: {
        // 检查登录状态
        checkLoginStatus: function() {
            return Auth.isAuthenticated() && Auth.getUserInfo() !== null;
        },

        // 登录
        login: async function(email, password) {
            try {
                const response = await fetch(CryptoApp.apiBaseUrl + '/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();
                console.log('登录响应:', data);

                if (data.status === 'success') {
                    // 保存token和用户信息
                    Auth.setAuth(data.data.token, {
                        user_id: data.data.user_id,
                        username: data.data.username,
                        email: data.data.email
                    });

                    console.log('准备跳转到仪表盘');
                    // 登录成功后直接跳转
                    setTimeout(() => {
                        window.location.href = '/frontend/pages/dashboard.html';
                    }, 100);
                    return { success: true, message: data.message };
                } else {
                    return { success: false, message: data.message || '登录失败' };
                }
            } catch (error) {
                console.error('登录请求失败:', error);
                return { success: false, message: '登录请求失败，请稍后重试' };
            }
        },

        // 注册
        register: async function(userData) {
            try {
                const response = await fetch(CryptoApp.apiBaseUrl + '/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(userData)
                });

                const data = await response.json();

                if (data.status === 'success') {
                    // 注册成功后自动登录
                    Auth.setAuth(data.data.token, {
                        user_id: data.data.user_id,
                        username: data.data.username,
                        email: data.data.email
                    });
                    return {
                        success: true,
                        user: data.data
                    };
                }

                return {
                    success: false,
                    message: data.message || '注册失败'
                };
            } catch (error) {
                console.error('注册失败:', error);
                return {
                    success: false,
                    message: error.message || '注册请求失败'
                };
            }
        },

        // 注销
        logout: function() {
            Auth.clearAuth();
            window.location.href = '/frontend/pages/login.html';
        },

        // 获取当前用户信息
        getCurrentUser: function() {
            return Auth.getUserInfo();
        }
    },

    // 主题管理
    theme: {
        // 获取当前主题
        getCurrent: function() {
            return localStorage.getItem('crypto_theme') || CryptoApp.config.theme;
        },

        // 设置主题
        set: function(theme) {
            if (theme === 'dark') {
                document.body.classList.remove('crypto-light-theme');
                document.body.classList.add('crypto-dark-theme');
            } else if (theme === 'light') {
                document.body.classList.remove('crypto-dark-theme');
                document.body.classList.add('crypto-light-theme');
            }

            localStorage.setItem('crypto_theme', theme);
            CryptoApp.config.theme = theme;
        }
    },

    // 工具函数
    utils: {
        // 格式化货币
        formatCurrency: function(value, currency = 'USD', maximumFractionDigits = 2) {
            if (typeof value !== 'number') {
                return value;
            }

            const formatter = new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: currency,
                maximumFractionDigits: maximumFractionDigits
            });

            return formatter.format(value);
        },

        // 格式化百分比
        formatPercent: function(value, digitsAfterDecimal = 2) {
            if (typeof value !== 'number') {
                return value;
            }

            return value.toFixed(digitsAfterDecimal) + '%';
        },

        // 格式化大数字
        formatNumber: function(value) {
            if (typeof value !== 'number') {
                return value;
            }

            if (value >= 1000000000) {
                return (value / 1000000000).toFixed(2) + 'B';
            } else if (value >= 1000000) {
                return (value / 1000000).toFixed(2) + 'M';
            } else if (value >= 1000) {
                return (value / 1000).toFixed(2) + 'K';
            }

            return value.toString();
        },

        // 格式化时间戳
        formatDate: function(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleDateString('zh-CN');
        },

        // 获取URL参数
        getUrlParam: function(param) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(param);
        },

        // 显示错误信息
        showError: function(message, container) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'alert alert-danger mt-3';
            errorDiv.role = 'alert';
            errorDiv.textContent = message;

            // 移除之前的错误信息
            const previousError = container.querySelector('.alert');
            if (previousError) {
                previousError.remove();
            }

            container.appendChild(errorDiv);

            // 3秒后自动移除错误信息
            setTimeout(() => {
                errorDiv.remove();
            }, 3000);
        }
    },

    // Toast 通知功能
    toast: {
        show: function(message, type = 'info') {
            const toastContainer = document.getElementById('toastContainer');
            if (!toastContainer) {
                // 创建 toast 容器
                const container = document.createElement('div');
                container.id = 'toastContainer';
                container.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999;';
                document.body.appendChild(container);
            }

            // 创建 toast 元素
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} mt-2`;
            toast.role = 'alert';
            toast.style.minWidth = '250px';
            toast.textContent = message;

            // 添加到容器
            document.getElementById('toastContainer').appendChild(toast);

            // 3秒后自动消失
            setTimeout(() => {
                toast.remove();
                // 如果容器为空，也移除容器
                const container = document.getElementById('toastContainer');
                if (container && container.children.length === 0) {
                    container.remove();
                }
            }, 3000);
        },

        success: function(message) {
            this.show(message, 'success');
        },

        error: function(message) {
            this.show(message, 'danger');
        },

        warning: function(message) {
            this.show(message, 'warning');
        },

        info: function(message) {
            this.show(message, 'info');
        }
    },

    // 初始化应用
    init: function() {
        // 应用主题
        const savedTheme = this.theme.getCurrent();
        this.theme.set(savedTheme);

        // 检查登录状态
        const isLoggedIn = this.user.checkLoginStatus();

        // 根据页面类型执行不同的初始化逻辑
        const currentPage = window.location.pathname.split('/').pop();

        switch (currentPage) {
            case 'login.html':
            case 'register.html':
                if (isLoggedIn) {
                    window.location.href = this.config.paths.dashboard;
                }
                break;

            default:
                if (!isLoggedIn && currentPage !== 'login.html' && currentPage !== 'register.html') {
                    window.location.href = this.config.paths.login;
                }
                break;
        }
    }
};

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', function() {
    CryptoApp.init(); // 启用登录状态检查

    // 处理登录表单提交
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            const result = await CryptoApp.user.login(email, password, rememberMe);

            if (result.success) {
                window.location.href = CryptoApp.config.paths.dashboard;
            } else {
                CryptoApp.utils.showError(result.message, loginForm);
            }
        });
    }

    // 处理注册表单提交
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const userData = {
                email: document.getElementById('email').value,
                username: document.getElementById('username').value,
                password: document.getElementById('password').value,
                invite_code: document.getElementById('inviteCode').value
            };

            const result = await CryptoApp.user.register(userData);

            if (result.success) {
                window.location.href = CryptoApp.config.paths.dashboard;
            } else {
                CryptoApp.utils.showError(result.message, registerForm);
            }
        });
    }
});
document.addEventListener('DOMContentLoaded', function() {
    // 加载导航栏
    loadNavbar();
    
    // 设置当前页面的导航项为激活状态
    setActiveNavItem();
});

// 加载导航栏
async function loadNavbar() {
    try {
        const response = await fetch('../components/navbar.html');
        const html = await response.text();
        document.querySelector('body').insertAdjacentHTML('afterbegin', html);
        
        // 初始化用户菜单功能
        initUserMenu();
        
        // 加载用户信息
        loadUserInfo();

        // 初始化帮助按钮
        initHelpButton();
    } catch (error) {
        console.error('加载导航栏失败:', error);
    }
}

// 设置当前页面的导航项为激活状态
function setActiveNavItem() {
    const currentPage = window.location.pathname.split('/').pop() || 'dashboard.html';
    const navItems = document.querySelectorAll('.crypto-nav-item');
    
    navItems.forEach(item => {
        const href = item.getAttribute('href');
        if (href === currentPage) {
            item.classList.add('active');
        } else {
            item.classList.remove('active');
        }
    });
}

// 初始化用户菜单功能
function initUserMenu() {
    const userMenu = document.querySelector('.crypto-user-menu');
    const userDropdown = document.querySelector('.crypto-user-dropdown');
    const searchForm = document.querySelector('.crypto-search-box');

    // 点击其他地方关闭下拉菜单
    document.addEventListener('click', function(event) {
        if (!userMenu.contains(event.target)) {
            userDropdown.style.display = 'none';
        }
    });

    // 点击用户菜单切换下拉菜单
    userMenu.addEventListener('click', function(event) {
        event.stopPropagation();
        const isVisible = userDropdown.style.display === 'block';
        userDropdown.style.display = isVisible ? 'none' : 'block';
    });

    // 处理搜索表单提交
    searchForm.addEventListener('submit', function(event) {
        event.preventDefault();
        const searchInput = this.querySelector('input[name="q"]');
        const query = searchInput.value.trim();
        
        if (query) {
            window.location.href = `search.html?q=${encodeURIComponent(query)}`;
        }
    });

    // 处理个人资料点击
    const profileBtn = document.getElementById('profile-btn');
    profileBtn.addEventListener('click', function(event) {
        event.preventDefault();
        showProfileModal();
    });

    // 处理账户设置点击
    const settingsBtn = document.getElementById('settings-btn');
    settingsBtn.addEventListener('click', function(event) {
        event.preventDefault();
        showSettingsModal();
    });

    // 处理退出登录
    const logoutButton = document.getElementById('logout-btn');
    logoutButton.addEventListener('click', async function(event) {
        event.preventDefault();
        try {
            const response = await fetch('/api/v1/auth/logout', {
                method: 'POST',
                credentials: 'include'
            });
            
            if (response.ok) {
                localStorage.removeItem('user');
                localStorage.removeItem('token');
                window.location.href = 'login.html';
            } else {
                throw new Error('退出登录失败');
            }
        } catch (error) {
            console.error('退出登录失败:', error);
            // 即使API调用失败，也清除本地存储并跳转
            localStorage.removeItem('user');
            localStorage.removeItem('token');
            window.location.href = 'login.html';
        }
    });
}

// 初始化帮助按钮
function initHelpButton() {
    const helpBtn = document.getElementById('help-btn');
    helpBtn.addEventListener('click', function(event) {
        event.preventDefault();
        showHelpModal();
    });
}

// 加载用户信息
async function loadUserInfo() {
    try {
        const userInfo = JSON.parse(localStorage.getItem('user'));
        if (userInfo) {
            document.getElementById('user-name').textContent = userInfo.username;
        } else {
            throw new Error('未找到用户信息');
        }
    } catch (error) {
        console.error('加载用户信息失败:', error);
        // 如果获取用户信息失败，重定向到登录页面
        window.location.href = 'login.html';
    }
}

// 显示个人资料模态框
function showProfileModal() {
    // TODO: 实现个人资料模态框
    console.log('打开个人资料');
}

// 显示设置模态框
function showSettingsModal() {
    // TODO: 实现设置模态框
    console.log('打开设置');
}

// 显示帮助模态框
function showHelpModal() {
    // TODO: 实现帮助模态框
    console.log('打开帮助');
} 
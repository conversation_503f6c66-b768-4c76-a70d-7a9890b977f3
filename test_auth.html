<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录状态测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .logout-btn {
            background-color: #dc3545;
        }
        .logout-btn:hover {
            background-color: #c82333;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 登录状态检测工具</h1>
        
        <div class="actions">
            <button onclick="checkAuthStatus()">🔍 检查登录状态</button>
            <button onclick="checkLocalStorage()">💾 检查本地存储</button>
            <button onclick="testCurrentUserAPI()">🌐 测试用户API</button>
            <button onclick="clearAuth()" class="logout-btn">🗑️ 清除认证信息</button>
        </div>
        
        <div id="results"></div>
        
        <div style="margin-top: 30px;">
            <h3>快速操作：</h3>
            <button onclick="window.open('frontend/pages/login.html')">🔑 打开登录页面</button>
            <button onclick="window.open('frontend/pages/register.html')">📝 打开注册页面</button>
            <button onclick="window.open('frontend/pages/dashboard.html')">📊 打开仪表盘</button>
        </div>
    </div>

    <script src="frontend/assets/js/utils/auth.js"></script>
    <script src="frontend/assets/js/app.js"></script>
    <script>
        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        function checkAuthStatus() {
            clearResults();
            
            const isAuthenticated = Auth.isAuthenticated();
            const userInfo = Auth.getUserInfo();
            const token = Auth.getToken();
            
            showResult(`<strong>认证状态:</strong> ${isAuthenticated ? '已登录' : '未登录'}`, 
                      isAuthenticated ? 'success' : 'error');
            
            if (token) {
                showResult(`<strong>Token:</strong> ${token.substring(0, 20)}...`, 'info');
            } else {
                showResult('<strong>Token:</strong> 无', 'error');
            }
            
            if (userInfo) {
                showResult(`<strong>用户信息:</strong><pre>${JSON.stringify(userInfo, null, 2)}</pre>`, 'success');
            } else {
                showResult('<strong>用户信息:</strong> 无', 'error');
            }
            
            // 检查CryptoApp的登录状态
            const cryptoAppStatus = CryptoApp.user.checkLoginStatus();
            showResult(`<strong>CryptoApp登录状态:</strong> ${cryptoAppStatus ? '已登录' : '未登录'}`, 
                      cryptoAppStatus ? 'success' : 'error');
        }
        
        function checkLocalStorage() {
            clearResults();
            
            const allKeys = Object.keys(localStorage);
            const authKeys = allKeys.filter(key => 
                key.includes('auth') || key.includes('user') || key.includes('token')
            );
            
            showResult(`<strong>所有localStorage键:</strong><pre>${JSON.stringify(allKeys, null, 2)}</pre>`, 'info');
            
            if (authKeys.length > 0) {
                const authData = {};
                authKeys.forEach(key => {
                    authData[key] = localStorage.getItem(key);
                });
                showResult(`<strong>认证相关数据:</strong><pre>${JSON.stringify(authData, null, 2)}</pre>`, 'info');
            } else {
                showResult('<strong>认证相关数据:</strong> 无', 'error');
            }
        }
        
        async function testCurrentUserAPI() {
            clearResults();
            
            try {
                showResult('正在调用 /auth/current-user API...', 'info');
                
                const response = await fetch('http://localhost:8000/api/v1/auth/current-user', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include'
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`<strong>API响应成功:</strong><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                } else {
                    showResult(`<strong>API响应失败:</strong><pre>${JSON.stringify(data, null, 2)}</pre>`, 'error');
                }
            } catch (error) {
                showResult(`<strong>API调用错误:</strong> ${error.message}`, 'error');
            }
        }
        
        function clearAuth() {
            Auth.clearAuth();
            showResult('认证信息已清除', 'success');
            setTimeout(() => {
                checkAuthStatus();
            }, 500);
        }
        
        // 页面加载时自动检查状态
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthStatus();
        });
    </script>
</body>
</html>

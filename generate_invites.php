<?php
require_once 'api/v1/config/database.php';

use Config\Database;

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>生成邀请码工具</h2>";
    
    // 先查看当前邀请码状态
    $query = "SELECT invite_code, status, used_by, created_at FROM invites ORDER BY created_at DESC";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $existing_invites = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>当前邀请码状态：</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
    echo "<tr><th>邀请码</th><th>状态</th><th>使用者ID</th><th>创建时间</th></tr>";
    foreach ($existing_invites as $invite) {
        $status_color = $invite['status'] === 'active' ? 'green' : 'red';
        echo "<tr>";
        echo "<td>{$invite['invite_code']}</td>";
        echo "<td style='color: {$status_color}'>{$invite['status']}</td>";
        echo "<td>" . ($invite['used_by'] ?: '未使用') . "</td>";
        echo "<td>{$invite['created_at']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 生成新的邀请码
    $new_invites = [
        'TEST2025',
        'DEMO2025', 
        'CRYPTO01',
        'CRYPTO02',
        'CRYPTO03',
        'BITCOIN01',
        'ETHEREUM01',
        'STUDENT01',
        'STUDENT02',
        'STUDENT03',
        'COURSE2025',
        'COM6023M01',
        'COM6023M02',
        'COM6023M03',
        'TRACKER01',
        'TRACKER02',
        'WELCOME01',
        'WELCOME02',
        'NEWUSER01',
        'NEWUSER02'
    ];
    
    echo "<h3>正在生成新邀请码...</h3>";
    
    $insert_query = "INSERT INTO invites (invite_code, status, created_at) VALUES (?, 'active', NOW())";
    $insert_stmt = $db->prepare($insert_query);
    
    $success_count = 0;
    $error_count = 0;
    
    foreach ($new_invites as $code) {
        try {
            $insert_stmt->execute([$code]);
            echo "<p style='color: green;'>✅ 成功生成邀请码: {$code}</p>";
            $success_count++;
        } catch (PDOException $e) {
            if ($e->getCode() == 23000) { // 重复键错误
                echo "<p style='color: orange;'>⚠️ 邀请码已存在: {$code}</p>";
            } else {
                echo "<p style='color: red;'>❌ 生成邀请码失败: {$code} - {$e->getMessage()}</p>";
                $error_count++;
            }
        }
    }
    
    echo "<hr>";
    echo "<h3>生成结果统计：</h3>";
    echo "<p>✅ 成功生成: {$success_count} 个邀请码</p>";
    echo "<p>❌ 失败: {$error_count} 个邀请码</p>";
    
    // 再次查看更新后的邀请码
    $stmt->execute();
    $updated_invites = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $active_count = count(array_filter($updated_invites, function($invite) {
        return $invite['status'] === 'active';
    }));
    
    echo "<p><strong>当前可用邀请码总数: {$active_count} 个</strong></p>";
    
    echo "<h3>可用于测试的邀请码：</h3>";
    echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 5px;'>";
    foreach ($updated_invites as $invite) {
        if ($invite['status'] === 'active') {
            echo "<code style='background: #e0e0e0; padding: 2px 5px; margin: 2px; display: inline-block;'>{$invite['invite_code']}</code> ";
        }
    }
    echo "</div>";
    
    echo "<hr>";
    echo "<h3>测试建议：</h3>";
    echo "<ul>";
    echo "<li>使用任意一个上面的邀请码进行注册测试</li>";
    echo "<li>每个邀请码只能使用一次</li>";
    echo "<li>注册成功后，邀请码状态会变为 'used'</li>";
    echo "<li>可以访问 <a href='test_register.html'>注册测试页面</a> 进行测试</li>";
    echo "<li>也可以访问 <a href='frontend/pages/register.html'>正式注册页面</a> 进行测试</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>";
}
?>

// API 基础URL
const API_BASE_URL = 'http://localhost:8000/api/v1';

// API 请求工具类
class Api {
    // 发送请求的通用方法
    static async request(endpoint, options = {}) {
        const url = API_BASE_URL + endpoint;

        // 默认请求配置
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            credentials: 'include' // 包含 cookies
        };

        // 合并配置
        const finalOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...(options.headers || {})
            }
        };

        try {
            const response = await fetch(url, finalOptions);

            // 检查响应类型
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                throw new Error('服务器返回了非JSON格式的数据');
            }

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || '请求失败');
            }

            return data;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    // 用户注册
    static async register(userData) {
        return this.request('/auth/register', {
            method: 'POST',
            body: JSON.stringify(userData)
        });
    }

    // 用户登录
    static async login(credentials) {
        return this.request('/auth/login', {
            method: 'POST',
            body: JSON.stringify(credentials)
        });
    }

    // 用户登出
    static async logout() {
        return this.request('/auth/logout', {
            method: 'POST'
        });
    }

    // 获取仪表盘所有数据
    static async getDashboardData() {
        return this.request('/dashboard/data');
    }

    // 获取市场统计数据
    static async getMarketStats() {
        return this.request('/market/stats');
    }

    // 获取热门币种数据
    static async getHotCoins() {
        return this.request('/market/hot-coins');
    }

    // 获取市场列表数据
    static async getMarketList() {
        return this.request('/market/list');
    }

    // 获取最新上市币种
    static async getNewListings() {
        return this.request('/market/new-listings');
    }

    // 获取最大涨幅币种
    static async getTopGainers() {
        return this.request('/market/top-gainers');
    }
}
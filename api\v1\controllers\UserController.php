<?php
namespace Controllers;

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../models/Invite.php';
require_once __DIR__ . '/../utils/ApiResponse.php';
require_once __DIR__ . '/../utils/response.php';

use Utils\Response;
use Config\Database;
use Models\User;
use Models\Invite;

class UserController {
    private $db;
    private $user;
    private $invite;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->user = new User($this->db);
        $this->invite = new Invite($this->db);
    }

    /**
     * 用户注册
     */
    public function register() {
        try {
            // 获取POST数据
            $data = json_decode(file_get_contents('php://input'), true);

            // 验证必填字段
            if (!isset($data['username']) || !isset($data['password']) || !isset($data['email']) || !isset($data['invite_code'])) {
                echo Response::error('用户名、邮箱、密码和邀请码不能为空', 400);
                return;
            }

            // 基本验证
            if (strlen($data['username']) < 3) {
                echo Response::error('用户名至少需要3个字符', 400);
                return;
            }

            if (strlen($data['password']) < 6) {
                echo Response::error('密码至少需要6个字符', 400);
                return;
            }

            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                echo Response::error('邮箱格式不正确', 400);
                return;
            }

            // 验证邀请码
            $inviteResult = $this->invite->validate($data['invite_code']);
            if (!$inviteResult['valid']) {
                echo Response::error($inviteResult['message'], 400);
                return;
            }

            // 设置用户信息
            $this->user->username = $data['username'];
            $this->user->email = $data['email'];
            $this->user->password = $data['password'];

            // 创建用户
            $createResult = $this->user->create();

            if ($createResult['success']) {
                // 标记邀请码为已使用
                $this->invite->markAsUsed($createResult['user_id']);

                // 生成token并设置session（注册后自动登录）
                $token = md5(uniqid() . time());
                session_start();
                $_SESSION['user_id'] = $createResult['user_id'];
                $_SESSION['email'] = $data['email'];
                $_SESSION['username'] = $data['username'];

                echo Response::success([
                    'token' => $token,
                    'user_id' => $createResult['user_id'],
                    'username' => $data['username'],
                    'email' => $data['email']
                ], '注册成功');
            } else {
                echo Response::error($createResult['message'], 400);
            }

        } catch (\Exception $e) {
            echo Response::error($e->getMessage(), 500);
        }
    }

    /**
     * 用户登录
     */
    public function login() {
        try {
            // 获取POST数据
            $data = json_decode(file_get_contents('php://input'), true);

            if (!isset($data['email']) || !isset($data['password'])) {
                echo Response::badRequest('邮箱和密码不能为空');
                return;
            }

            // 设置用户信息并尝试登录
            $this->user->email = $data['email'];
            $this->user->password = $data['password'];

            $result = $this->user->login();

            if ($result['success']) {
                // 生成token
                $token = md5(uniqid() . time());

                // 登录成功，设置session
                session_start();
                $_SESSION['user_id'] = $result['user_id'];
                $_SESSION['email'] = $result['email'];
                $_SESSION['username'] = $result['username'];

                // 返回用户信息和token
                echo Response::success([
                    'token' => $token,
                    'user_id' => $result['user_id'],
                    'username' => $result['username'],
                    'email' => $result['email']
                ], '登录成功');
            } else {
                echo Response::error($result['message']);
            }

        } catch (\Exception $e) {
            echo Response::error($e->getMessage());
        }
    }

    /**
     * 用户登出
     */
    public function logout() {
        try {
            // 启动session
            session_start();

            // 清除所有session数据
            $_SESSION = [];

            // 如果使用了session cookie，也删除它
            if (ini_get("session.use_cookies")) {
                $params = session_get_cookie_params();
                setcookie(session_name(), '', time() - 42000,
                    $params["path"], $params["domain"],
                    $params["secure"], $params["httponly"]
                );
            }

            // 销毁session
            session_destroy();

            echo Response::success(null, '登出成功');
        } catch (\Exception $e) {
            echo Response::error($e->getMessage());
        }
    }

    /**
     * 验证邀请码
     */
    public function validateInvite() {
        try {
            // 获取POST数据
            $data = json_decode(file_get_contents('php://input'), true);

            if (!isset($data['invite_code'])) {
                echo Response::error('邀请码不能为空', 400);
                return;
            }

            // 验证邀请码
            $inviteResult = $this->invite->validate($data['invite_code']);

            if ($inviteResult['valid']) {
                echo Response::success(null, '邀请码验证成功');
            } else {
                echo Response::error($inviteResult['message'], 400);
            }

        } catch (\Exception $e) {
            echo Response::error($e->getMessage(), 500);
        }
    }

    public function getCurrentUser() {
        session_start();
        if (!isset($_SESSION['user_id'])) {
            echo Response::error("未登录", 401);
            return;
        }

        $this->user->user_id = $_SESSION['user_id'];
        $userInfo = $this->user->getInfo();

        if ($userInfo) {
            echo Response::success($userInfo, "获取用户信息成功");
        } else {
            echo Response::error("用户信息不存在", 404);
        }
    }
}
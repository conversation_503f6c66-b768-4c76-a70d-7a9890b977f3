php -S localhost:8000 -t .

# 📈 CryptoTracker 中文说明文档

一个基于 **PHP + MySQL** 构建的全栈加密货币数据可视化网站，包含 **邀请码注册机制** 和 **实时行情图表前端**，前端使用 HTML + JavaScript + Chart.js + Bootstrap。

---

## 🎯 项目目的

本项目用于完成 **高级 Web 开发课程作业（COM6023M）**，体现以下能力：

- 使用 PHP 开发 RESTful API
- 通过邀请码注册机制保障用户访问
- 前端实时渲染加密货币数据（行情图表）
- 用户可搜索加密货币并添加关注列表
- 系统具备可部署性、可维护性、安全性

---

## 🛠️ 技术栈

| 层级        | 技术                                     |
|-------------|------------------------------------------|
| 后端        | 原生 PHP                                 |
| 数据库      | MySQL + PDO                              |
| 前端        | HTML, JavaScript, Bootstrap, Chart.js    |
| 数据提取    | Python (e.g., finlab_crypto, ccxt, requests) |
| 部署环境    | XAMPP / Apache                           |

---

## 🖼️ 前端页面与功能模块

本项目采用现代高级UI设计风格，主要基于Bootstrap 5构建，结合Chart.js实现数据可视化。前端采用模块化设计，使用模拟数据进行开发，为后端API集成做好准备。

### 文件结构
```
/frontend
├── assets/
│   ├── css/          # 样式文件
│   ├── js/           # JavaScript脚本
│   ├── img/          # 图像资源
├── pages/            # HTML页面
```

### 页面与功能列表

| 页面分类     | 页面/功能描述                                           | 开发状态     |
|--------------|--------------------------------------------------------|--------------|
| 🔑 认证页面   | 登录页面 - 简洁现代的表单设计，深色主题                  | 🔲 未完成     |
|              | 注册页面 - 带邀请码验证的分步注册流程                   | 🔲 未完成     |
|              | 忘记密码页面 - 密码重置功能                             | 🔲 未完成     |
| 📊 主要页面   | 仪表盘/首页 - 市场概览与自选币快速访问                  | 🔲 未完成     |
|              | 币种详情页 - 完整K线图与详细信息展示                    | 🔲 未完成     |
|              | 搜索结果页 - 响应式列表与卡片式搜索结果                 | 🔲 未完成     |
|              | 自选币管理页 - 用户关注的加密货币管理                   | 🔲 未完成     |
| 📈 图表组件   | K线图(蜡烛图)组件 - 可调时间范围的价格走势              | 🔲 未完成     |
|              | 交易量柱状图 - 与K线图联动                              | 🔲 未完成     |
|              | 市值分布饼图 - 可视化市场份额                           | 🔲 未完成     |
|              | 价格趋势图 - 不同币种价格比较                           | 🔲 未完成     |
| 🧩 UI组件     | 导航栏组件 - 响应式设计，含搜索功能                     | 🔲 未完成     |
|              | 币种卡片组件 - 显示基本信息和快速操作                   | 🔲 未完成     |
|              | 市场概览横幅 - 顶部滚动展示主要币种                     | 🔲 未完成     |
|              | 消息通知组件 - 操作反馈与系统消息                       | 🔲 未完成     |
| 🔄 交互功能   | 深色/浅色主题切换 - 用户界面主题偏好                    | 🔲 未完成     |
|              | 实时数据更新模拟 - 价格变化动态效果                     | 🔲 未完成     |
|              | 添加/删除自选币 - 用户收藏功能                          | 🔲 未完成     |
|              | 响应式布局适配 - 移动端与桌面端体验优化                 | 🔲 未完成     |
| 🔌 API集成准备 | API请求模块 - 封装AJAX/Fetch请求                       | 🔲 未完成     |
|              | API响应处理 - 数据格式化与错误处理                      | 🔲 未完成     |
|              | 用户认证状态管理 - 登录状态维护                         | 🔲 未完成     |
|              | 模拟数据配置 - 开发阶段使用的测试数据                   | 🔲 未完成     |

### 设计特色

- **高级现代UI**: 采用深色背景搭配霓虹色调点缀，营造专业金融产品感
- **响应式设计**: 完美适配移动端、平板与桌面设备
- **交互动效**: 页面转场动画与微交互反馈增强用户体验
- **可视化重点**: 专业的金融数据图表与实时数据更新效果
- **模块化结构**: 便于维护与扩展的组件化设计

---

## 🐍 Python 数据提取模块 (规划)

为了给 CryptoTracker 提供准确、及时的加密货币数据，我们计划使用 Python 开发一个独立的数据提取模块。该模块将负责：

1.  **历史数据获取**：
    *   从主流加密货币交易所API（如 Binance, Kraken, Coinbase）或聚合数据提供商API（如 CoinGecko, CoinMarketCap, CryptoCompare）获取主要加密货币（如 BTC, ETH, ADA, SOL 等）的历史K线数据（OHLCV - 开盘价, 最高价, 最低价, 收盘价, 交易量）。
    *   数据可以按不同时间粒度（如每日、每小时）获取。
    *   考虑使用 `finlab_crypto` 或 `ccxt` 库来简化与交易所API的交互。
    *   获取到的历史数据将存储在我们的MySQL数据库中，供PHP后端查询。

2.  **实时数据获取**：
    *   从上述API获取主流加密货币的实时价格、24小时涨跌幅、交易量等信息。
    *   对于频繁更新的数据，可以考虑设置定时任务（如 Cron Job）来定期刷新数据到数据库。

3.  **数据预处理与存储**：
    *   对获取的数据进行清洗和格式化，以适应数据库存储和前端展示的需求。
    *   设计合适的数据库表结构来存储历史K线数据和加密货币基本信息。

**数据流初步设想：**
`Python Scripts (finlab_crypto/ccxt/requests) -> MySQL Database -> PHP Backend (RESTful API) -> JavaScript Frontend (Chart.js)`

**备选库/工具：**
-   `requests`: 用于直接调用HTTP API。
-   `pandas`: 用于数据处理和分析。
-   `APScheduler`: 用于在Python中管理定时任务。

*此模块的具体实现细节将在后续开发阶段进一步细化。*

---

## 📁 安装说明

1. **复制或克隆项目**到你的 XAMPP 安装目录下的 `htdocs`：


2. **数据库设置**：
   - 打开 phpMyAdmin
   - 导入 `cryptotracker.sql`（建表脚本，包括 `users`、`invites`、`subscriptions`、`crypto_currencies`, `crypto_history`）
   - 示例邀请码：`INVITE2025`, `ALPHAENTRY`

3. 使用 XAMPP 控制面板启动 Apache 和 MySQL

4. 在浏览器访问应用：


---

## 🔐 演示登录凭据（可在演示视频中使用）

| 邮箱                | 密码     | 邀请码         |
|---------------------|----------|----------------|
| <EMAIL>  | feng0521  | ALPHAENTRY     |

---

## 🚀 功能特色

- 🧾 支持邀请码注册机制
- 🔐 登录系统支持密码加密存储（bcrypt）
- 📊 接入外部加密货币 API 或通过Python脚本获取实时数据
- 📈 图表展示加密货币行情（基于 Chart.js）
- ⭐ 用户可添加自选加密货币收藏
- ✅ 使用 Session 实现用户登录状态管理
- 🧠 错误处理与输入验证机制
- 🔍 加密货币代码/名称搜索功能
- 🌐 完整文档化 RESTful API
- 🐍 Python 模块负责获取和预处理加密货币数据

---

## 📖 后端 API 接口列表

## ✅ CryptoTracker 功能开发进度自检清单（基于 COM6023M 要求）

| 模块分类           | 功能项描述                                                         | 开发状态     |
|--------------------|----------------------------------------------------------------------|--------------|
| 🔐 用户系统         | 用户使用邀请码注册账户                                               | 🔲 未完成     |
|                    | 用户登录验证（基于邮箱+密码）                                       | 🔲 未完成     |
|                    | 密码加密存储（bcrypt）                                              | 🔲 未完成     |
|                    | 登录后会话管理（Session / Token）                                   | 🔲 未完成     |
|                    | 输入验证与错误提示（前后端）                                        | 🔲 未完成     |
|                    | 登录状态持久化处理                                                  | 🔲 未完成     |
| 📈 加密货币数据展示 | 展示主流加密货币概览（如 BTC, ETH 市值、24h涨跌幅）                 | 🔲 未完成     |
|                    | 查询单种加密货币数据（历史K线、实时价格，数据源自Python模块/DB）      | 🔲 未完成     |
|                    | 加密货币数据图表可视化（Chart.js，例如价格K线图、成交量图）         | 🔲 未完成     |
|                    | 加密货币搜索功能（按ID/代码/名称查询）                               | 🔲 未完成     |
| ⭐ 自选币功能       | 添加自选加密货币到关注列表                                           | 🔲 未完成     |
|                    | 显示当前用户关注的加密货币清单                                       | 🔲 未完成     |
|                    | 删除已关注的加密货币                                                 | 🔲 未完成     |
| 🐍 Python数据提取  | 实现Python脚本获取指定加密货币的历史K线数据 (OHLCV)                | 🔲 未完成     |
|                    | 实现Python脚本获取主流加密货币的实时价格和基本市场数据               | 🔲 未完成     |
|                    | 将Python获取的数据存入MySQL数据库的相应表中                        | 🔲 未完成     |
|                    | (可选) 设置定时任务自动更新数据库中的加密货币数据                    | 🔲 未完成     |
| 🧠 系统架构设计     | 使用 MVC 模式组织PHP后端代码结构                                   | 🔲 未完成     |
|                    | 后端使用原生 PHP + PDO 操作 MySQL (从Python模块填充的数据库读取数据) | 🔲 未完成     |
|                    | 前端使用 HTML + Bootstrap + JS                                      | 🔲 未完成     |
|                    | 使用 .htaccess 实现 URL 重写                                        | 🔲 未完成     |
| 🛡️ 安全与鲁棒性     | 所有输入字段进行验证（邮箱格式、密码长度等）                          | 🔲 未完成     |
|                    | 防止 SQL 注入（使用 PDO 预处理语句）                                  | 🔲 未完成     |
|                    | 错误处理和异常响应（状态码、提示信息）                                  | 🔲 未完成     |
| 📦 部署与运行       | 项目可在本地 XAMPP Apache 环境运行                                     | 🔲 未完成     |
|                    | 数据库结构通过 SQL 脚本一键导入                                        | 🔲 未完成     |
|                    | README 中包含完整安装说明                                              | 🔲 未完成     |
| 🧪 API 服务接口     | `/register`：注册（带邀请码）                                           | 🔲 未完成     |
|                    | `/login`：登录验证                                                      | 🔲 未完成     |
|                    | `/market_overview`：获取主流加密货币市场概览数据                        | 🔲 未完成     |
|                    | `/crypto?id=[crypto_id]`：获取特定加密货币的详细数据（历史+实时）       | 🔲 未完成     |
|                    | `/favorites`：获取用户关注的加密货币列表                                 | 🔲 未完成     |
|                    | `/favorites`（POST）：添加关注加密货币                                     | 🔲 未完成     |
|                    | `/favorites/{crypto_id}`（DELETE）：删除关注的加密货币                    | 🔲 未完成     |

---

## 📦 外部依赖

- [Chart.js](https://www.chartjs.org/)（用于图表渲染）
- Python Libraries: [finlab_crypto](https://github.com/finlab-python/finlab_crypto), [ccxt](https://github.com/ccxt/ccxt), [requests](https://requests.readthedocs.io/en/master/) (for data fetching)
- 加密货币数据API: CoinGecko API, CoinMarketCap API, Binance API, etc. (for data sourcing)
- Bootstrap 5（CDN 加载）
- Apache Rewrite 模块（支持 `.htaccess`）

---

## 📹 视频演示要求（提交作业必须）

> 演示视频时长应为 5–10 分钟

**需展示内容：**

- 使用邀请码进行注册
- 登录后查看加密货币市场概览
- 搜索特定加密货币并显示其历史价格图表和实时数据
- 添加/移除关注列表功能
- 展示所有服务器端响应（JSON、状态码等）
- 讲解代码结构（PHP MVC）、数据库连接、Python数据提取流程简介
- 展示安全处理措施（如防 SQL 注入）
- 部署路径和访问方式（localhost 或公网地址）

---

## 📝 作业要求完成度自检清单（COM6023M）

| 要求项目                                                | 当前状态     |
|----------------------------------------------------------|--------------|
| ✅ 使用 PHP 构建 REST Web 服务                          | ⚪ 规划中     |
| ✅ 使用 HTML/JS 构建前端客户端                          | ⚪ 规划中     |
| ✅ 已连接托管数据库（MySQL）                           | ⚪ 规划中     |
| ✅ 注册必须通过邀请码验证                              | ⚪ 规划中     |
| ✅ 场景贴近真实世界（加密货币市场）                    | ✅ 主题已定   |
| ✅ 提供完整 CRUD 操作 REST API                         | ⚪ 规划中     |
| ✅ 可部署于本地 XAMPP Apache 服务器                    | ⚪ 规划中     |
| ✅ 已包含代码、文档、演示视频                          | ⚪ 待完成     |
| ✅ 密码加密存储（bcrypt）                              | ⚪ 规划中     |
| ✅ 完整的 README 安装和使用说明                        | 🟡 更新中     |
| ✅ 包含基本安全验证和输入校验                          | ⚪ 规划中     |
| ✅ (新增) Python模块提取加密货币数据                     | ⚪ 规划中     |

---


## 📚 参考资源

- [Chart.js 文档](https://www.chartjs.org/)
- [PHP PDO 官方手册](https://www.php.net/manual/zh/book.pdo.php)
- [finlab_crypto on GitHub](https://github.com/finlab-python/finlab_crypto)
- [CCXT Library](https://github.com/ccxt/ccxt)
- [CoinGecko API](https://www.coingecko.com/en/api)
- [CoinMarketCap API](https://coinmarketcap.com/api/)
- [Bootstrap 官网](https://getbootstrap.com/)

本项目中所用外部库及代码均遵循其原许可协议，若有引用将于代码注释中标明。


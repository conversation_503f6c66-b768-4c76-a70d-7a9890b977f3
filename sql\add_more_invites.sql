-- 添加更多邀请码用于测试
-- 使用方法：在phpMyAdmin中执行此SQL，或者通过命令行导入

USE F89;

-- 插入更多测试邀请码
INSERT INTO invites (invite_code, status, created_at) VALUES
('TEST2025', 'active', NOW()),
('DEMO2025', 'active', NOW()),
('CRYPTO01', 'active', NOW()),
('CRYPTO02', 'active', NOW()),
('CRYPTO03', 'active', NOW()),
('CRYPTO04', 'active', NOW()),
('CRYPTO05', 'active', NOW()),
('BITCOIN01', 'active', NOW()),
('BITCOIN02', 'active', NOW()),
('ETHEREUM01', 'active', NOW()),
('ETHEREUM02', 'active', NOW()),
('STUDENT01', 'active', NOW()),
('STUDENT02', 'active', NOW()),
('STUDENT03', 'active', NOW()),
('STUDENT04', 'active', NOW()),
('STUDENT05', 'active', NOW()),
('COURSE2025', 'active', NOW()),
('COM6023M01', 'active', NOW()),
('COM6023M02', 'active', NOW()),
('COM6023M03', 'active', NOW()),
('COM6023M04', 'active', NOW()),
('COM6023M05', 'active', NOW()),
('TRACKER01', 'active', NOW()),
('TRACKER02', 'active', NOW()),
('TRACKER03', 'active', NOW()),
('WELCOME01', 'active', NOW()),
('WELCOME02', 'active', NOW()),
('WELCOME03', 'active', NOW()),
('NEWUSER01', 'active', NOW()),
('NEWUSER02', 'active', NOW()),
('NEWUSER03', 'active', NOW()),
('TESTING01', 'active', NOW()),
('TESTING02', 'active', NOW()),
('TESTING03', 'active', NOW()),
('DEVELOP01', 'active', NOW()),
('DEVELOP02', 'active', NOW()),
('DEVELOP03', 'active', NOW()),
('REGISTER01', 'active', NOW()),
('REGISTER02', 'active', NOW()),
('REGISTER03', 'active', NOW()),
('SIGNUP01', 'active', NOW()),
('SIGNUP02', 'active', NOW()),
('SIGNUP03', 'active', NOW()),
('ACCESS01', 'active', NOW()),
('ACCESS02', 'active', NOW()),
('ACCESS03', 'active', NOW()),
('ENTRY01', 'active', NOW()),
('ENTRY02', 'active', NOW()),
('ENTRY03', 'active', NOW()),
('JOIN01', 'active', NOW()),
('JOIN02', 'active', NOW())
ON DUPLICATE KEY UPDATE 
    status = 'active',
    created_at = NOW();

-- 查看插入结果
SELECT 
    COUNT(*) as total_invites,
    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_invites,
    SUM(CASE WHEN status = 'used' THEN 1 ELSE 0 END) as used_invites
FROM invites;

-- 显示所有可用邀请码
SELECT invite_code, status, created_at 
FROM invites 
WHERE status = 'active' 
ORDER BY created_at DESC;

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录流程测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .status {
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 完整登录流程测试</h1>
        
        <div class="form-group">
            <label for="email">邮箱：</label>
            <input type="email" id="email" value="<EMAIL>" placeholder="输入邮箱">
        </div>
        
        <div class="form-group">
            <label for="password">密码：</label>
            <input type="password" id="password" value="password123" placeholder="输入密码">
        </div>
        
        <div class="actions">
            <button onclick="testLogin()">🔑 测试登录</button>
            <button onclick="checkAuthAfterLogin()">🔍 检查登录状态</button>
            <button onclick="testDashboardAccess()">📊 测试仪表盘访问</button>
            <button onclick="testLogout()">🚪 测试登出</button>
            <button onclick="clearAll()">🗑️ 清除所有数据</button>
        </div>
        
        <div id="results"></div>
        
        <div style="margin-top: 30px;">
            <h3>测试步骤：</h3>
            <ol>
                <li>点击"测试登录"进行登录</li>
                <li>点击"检查登录状态"验证登录是否成功</li>
                <li>点击"测试仪表盘访问"看是否能正常访问</li>
                <li>点击"测试登出"验证登出功能</li>
            </ol>
        </div>
    </div>

    <script src="frontend/assets/js/utils/auth.js"></script>
    <script src="frontend/assets/js/app.js"></script>
    <script>
        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testLogin() {
            clearResults();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                showResult('请输入邮箱和密码', 'error');
                return;
            }
            
            showResult('正在尝试登录...', 'info');
            
            try {
                const result = await CryptoApp.user.login(email, password);
                
                if (result.success) {
                    showResult(`登录成功！消息: ${result.message}`, 'success');
                    
                    // 检查本地存储
                    const token = Auth.getToken();
                    const userInfo = Auth.getUserInfo();
                    
                    if (token) {
                        showResult(`Token已保存: ${token.substring(0, 20)}...`, 'success');
                    }
                    
                    if (userInfo) {
                        showResult(`用户信息已保存: ${JSON.stringify(userInfo)}`, 'success');
                    }
                } else {
                    showResult(`登录失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult(`登录错误: ${error.message}`, 'error');
            }
        }
        
        function checkAuthAfterLogin() {
            clearResults();
            
            const isAuthenticated = Auth.isAuthenticated();
            const userInfo = Auth.getUserInfo();
            const cryptoAppStatus = CryptoApp.user.checkLoginStatus();
            
            showResult(`Auth.isAuthenticated(): ${isAuthenticated}`, isAuthenticated ? 'success' : 'error');
            showResult(`CryptoApp.user.checkLoginStatus(): ${cryptoAppStatus}`, cryptoAppStatus ? 'success' : 'error');
            
            if (userInfo) {
                showResult(`用户信息: ${JSON.stringify(userInfo)}`, 'success');
            } else {
                showResult('用户信息: 无', 'error');
            }
        }
        
        function testDashboardAccess() {
            clearResults();
            
            const isLoggedIn = CryptoApp.user.checkLoginStatus();
            
            if (isLoggedIn) {
                showResult('登录状态正常，可以访问仪表盘', 'success');
                showResult('正在打开仪表盘页面...', 'info');
                setTimeout(() => {
                    window.open('frontend/pages/dashboard.html', '_blank');
                }, 1000);
            } else {
                showResult('未登录，无法访问仪表盘', 'error');
                showResult('应该会重定向到登录页面', 'info');
            }
        }
        
        async function testLogout() {
            clearResults();
            
            try {
                showResult('正在调用登出API...', 'info');
                
                const response = await fetch('http://localhost:8000/api/v1/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include'
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`服务器登出成功: ${data.message}`, 'success');
                    
                    // 清除本地认证信息
                    Auth.clearAuth();
                    showResult('本地认证信息已清除', 'success');
                    
                    // 检查状态
                    const isStillLoggedIn = CryptoApp.user.checkLoginStatus();
                    showResult(`登录状态: ${isStillLoggedIn ? '仍然登录' : '已登出'}`, isStillLoggedIn ? 'error' : 'success');
                } else {
                    showResult(`登出失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult(`登出错误: ${error.message}`, 'error');
            }
        }
        
        function clearAll() {
            Auth.clearAuth();
            clearResults();
            showResult('所有数据已清除', 'success');
        }
        
        // 页面加载时显示当前状态
        document.addEventListener('DOMContentLoaded', function() {
            const isLoggedIn = CryptoApp.user.checkLoginStatus();
            if (isLoggedIn) {
                showResult('检测到已登录状态', 'success');
                const userInfo = Auth.getUserInfo();
                if (userInfo) {
                    showResult(`当前用户: ${userInfo.username} (${userInfo.email})`, 'info');
                }
            } else {
                showResult('当前未登录', 'info');
            }
        });
    </script>
</body>
</html>

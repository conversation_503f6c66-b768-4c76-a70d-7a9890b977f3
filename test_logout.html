<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试登出功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .logout-btn {
            background-color: #dc3545;
        }
        .logout-btn:hover {
            background-color: #c82333;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CryptoTracker 登出功能测试</h1>
        
        <div>
            <h3>步骤1: 先登录</h3>
            <button onclick="testLogin()">测试登录</button>
            <p>使用测试账号: <EMAIL> / feng0521</p>
        </div>
        
        <div>
            <h3>步骤2: 检查登录状态</h3>
            <button onclick="checkLoginStatus()">检查当前登录状态</button>
        </div>
        
        <div>
            <h3>步骤3: 测试登出</h3>
            <button class="logout-btn" onclick="testLogout()">测试登出功能</button>
        </div>
        
        <div>
            <h3>步骤4: 再次检查状态</h3>
            <button onclick="checkLoginStatus()">确认已登出</button>
        </div>
        
        <div id="result" class="result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api/v1';
        
        function showResult(message, isSuccess = true) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.style.display = 'block';
        }
        
        async function testLogin() {
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'feng0521'
                    })
                });
                
                const data = await response.json();
                console.log('登录响应:', data);
                
                if (data.status === 'success') {
                    showResult(`登录成功! 用户: ${data.data.username}`, true);
                } else {
                    showResult(`登录失败: ${data.message}`, false);
                }
            } catch (error) {
                showResult(`登录错误: ${error.message}`, false);
            }
        }
        
        async function testLogout() {
            try {
                const response = await fetch(`${API_BASE_URL}/auth/logout`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include'
                });
                
                const data = await response.json();
                console.log('登出响应:', data);
                
                if (data.status === 'success') {
                    showResult(`登出成功! ${data.message}`, true);
                } else {
                    showResult(`登出失败: ${data.message}`, false);
                }
            } catch (error) {
                showResult(`登出错误: ${error.message}`, false);
            }
        }
        
        async function checkLoginStatus() {
            try {
                const response = await fetch(`${API_BASE_URL}/auth/current-user`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include'
                });
                
                const data = await response.json();
                console.log('用户状态响应:', data);
                
                if (response.ok && data.status === 'success') {
                    showResult(`已登录状态 - 用户: ${data.data.username}`, true);
                } else {
                    showResult(`未登录状态: ${data.message}`, true);
                }
            } catch (error) {
                showResult(`检查状态错误: ${error.message}`, false);
            }
        }
    </script>
</body>
</html>

<?php
require_once 'api/v1/config/database.php';

use Config\Database;

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "数据库连接成功！<br>";
    
    // 测试查询邀请码
    $query = "SELECT * FROM invites WHERE status = 'active'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $invites = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "可用邀请码：<br>";
    foreach ($invites as $invite) {
        echo "- " . $invite['invite_code'] . " (状态: " . $invite['status'] . ")<br>";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表盘修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .monitor {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .api-test {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .api-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 仪表盘循环请求修复测试</h1>
        
        <div class="status warning">
            <strong>修复内容：</strong>
            <ul>
                <li>移除了重复的定时器设置</li>
                <li>将刷新频率从60秒改为120秒</li>
                <li>修复了navbar.js中的强制跳转逻辑</li>
                <li>添加了缺失的 /coins/list API路由</li>
                <li>防止重复初始化</li>
            </ul>
        </div>
        
        <div class="actions">
            <button onclick="testAPIs()">🧪 测试所有API</button>
            <button onclick="startMonitoring()">📊 开始监控请求</button>
            <button onclick="stopMonitoring()">⏹️ 停止监控</button>
            <button onclick="clearLog()">🗑️ 清除日志</button>
            <button onclick="openDashboard()">📈 打开仪表盘</button>
        </div>
        
        <div class="api-test">
            <div class="api-item">
                <h4>仪表盘API测试</h4>
                <button onclick="testAPI('/api/v1/dashboard/overview')">概览</button>
                <button onclick="testAPI('/api/v1/dashboard/market-table')">市场表</button>
                <button onclick="testAPI('/api/v1/coins/list')">币种列表</button>
            </div>
            <div class="api-item">
                <h4>认证API测试</h4>
                <button onclick="testAPI('/api/v1/auth/current-user')">当前用户</button>
                <button onclick="testAPI('/api/v1/auth/logout', 'POST')">登出</button>
            </div>
        </div>
        
        <div id="results"></div>
        
        <div class="monitor" id="requestMonitor">
            <strong>请求监控日志：</strong><br>
            <span style="color: #666;">点击"开始监控请求"来查看实时API调用情况</span>
        </div>
        
        <div style="margin-top: 30px;">
            <h3>测试步骤：</h3>
            <ol>
                <li>先测试各个API是否正常工作</li>
                <li>开始监控请求</li>
                <li>打开仪表盘页面</li>
                <li>观察是否还有疯狂的重复请求</li>
                <li>正常情况下应该只有初始加载和每2分钟的定时刷新</li>
            </ol>
        </div>
    </div>

    <script>
        let monitoringInterval;
        let requestCount = 0;
        let lastRequestTime = Date.now();
        
        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        function addToMonitor(message) {
            const monitor = document.getElementById('requestMonitor');
            const timestamp = new Date().toLocaleTimeString();
            monitor.innerHTML += `<br>[${timestamp}] ${message}`;
            monitor.scrollTop = monitor.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('requestMonitor').innerHTML = '<strong>请求监控日志：</strong><br>';
            requestCount = 0;
        }
        
        async function testAPI(endpoint, method = 'GET') {
            try {
                const response = await fetch(`http://localhost:8000${endpoint}`, {
                    method: method,
                    credentials: 'include'
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`✅ ${endpoint} - 成功`, 'success');
                    addToMonitor(`API测试成功: ${endpoint}`);
                } else {
                    showResult(`❌ ${endpoint} - 失败: ${data.message}`, 'error');
                    addToMonitor(`API测试失败: ${endpoint} - ${data.message}`);
                }
            } catch (error) {
                showResult(`❌ ${endpoint} - 错误: ${error.message}`, 'error');
                addToMonitor(`API测试错误: ${endpoint} - ${error.message}`);
            }
        }
        
        async function testAPIs() {
            clearResults();
            showResult('开始测试所有API...', 'info');
            
            const apis = [
                '/api/v1/dashboard/overview',
                '/api/v1/dashboard/market-table',
                '/api/v1/coins/list',
                '/api/v1/auth/current-user'
            ];
            
            for (const api of apis) {
                await testAPI(api);
                await new Promise(resolve => setTimeout(resolve, 500)); // 延迟500ms
            }
            
            showResult('API测试完成', 'success');
        }
        
        function startMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
            }
            
            clearLog();
            addToMonitor('开始监控请求频率...');
            
            // 监控网络请求（简单版本）
            const originalFetch = window.fetch;
            window.fetch = function(...args) {
                const url = args[0];
                if (typeof url === 'string' && url.includes('/api/v1/')) {
                    requestCount++;
                    const now = Date.now();
                    const timeSinceLastRequest = now - lastRequestTime;
                    lastRequestTime = now;
                    
                    addToMonitor(`请求 #${requestCount}: ${url} (距上次: ${timeSinceLastRequest}ms)`);
                    
                    if (timeSinceLastRequest < 1000 && requestCount > 1) {
                        addToMonitor(`⚠️ 警告: 请求间隔过短 (${timeSinceLastRequest}ms)`);
                    }
                }
                return originalFetch.apply(this, args);
            };
            
            showResult('请求监控已启动', 'success');
        }
        
        function stopMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
            }
            
            // 恢复原始fetch
            if (window.originalFetch) {
                window.fetch = window.originalFetch;
            }
            
            addToMonitor('监控已停止');
            showResult('请求监控已停止', 'info');
        }
        
        function openDashboard() {
            addToMonitor('打开仪表盘页面...');
            window.open('frontend/pages/dashboard.html', '_blank');
        }
        
        // 页面加载时显示状态
        document.addEventListener('DOMContentLoaded', function() {
            showResult('仪表盘修复测试工具已加载', 'info');
        });
    </script>
</body>
</html>

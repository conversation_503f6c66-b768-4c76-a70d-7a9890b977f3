class Auth {
    static TOKEN_KEY = 'auth_token';
    static USER_KEY = 'user_info';
    
    /**
     * 保存token和用户信息
     * @param {string} token JWT token
     * @param {object} userInfo 用户信息
     */
    static setAuth(token, userInfo) {
        localStorage.setItem(this.TOKEN_KEY, token);
        localStorage.setItem(this.USER_KEY, JSON.stringify(userInfo));
    }

    /**
     * 获取token
     * @returns {string|null}
     */
    static getToken() {
        return localStorage.getItem(this.TOKEN_KEY);
    }

    /**
     * 获取用户信息
     * @returns {object|null}
     */
    static getUserInfo() {
        const userInfo = localStorage.getItem(this.USER_KEY);
        return userInfo ? JSON.parse(userInfo) : null;
    }

    /**
     * 清除认证信息
     */
    static clearAuth() {
        localStorage.removeItem(this.TOKEN_KEY);
        localStorage.removeItem(this.USER_KEY);
    }

    /**
     * 检查是否已登录
     * @returns {boolean}
     */
    static isAuthenticated() {
        return !!this.getToken();
    }
}

// API请求工具类
class ApiClient {
    // 修改API基础路径
    static BASE_URL = 'http://localhost:8000/api/v1';

    /**
     * 发送API请求
     * @param {string} endpoint 
     * @param {object} options 
     * @returns {Promise}
     */
    static async request(endpoint, options = {}) {
        const token = Auth.getToken();
        const headers = {
            'Content-Type': 'application/json',
            ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
            ...options.headers
        };

        try {
            const response = await fetch(`${this.BASE_URL}${endpoint}`, {
                ...options,
                headers
            });

            const data = await response.json();

            if (!response.ok) {
                // 如果是认证错误，清除本地认证信息
                if (response.status === 401) {
                    Auth.clearAuth();
                    // 重定向到登录页
                    window.location.href = '/frontend/pages/login.html';
                }
                throw new Error(data.message || '请求失败');
            }

            return data;
        } catch (error) {
            console.error('API请求错误:', error);
            throw error;
        }
    }

    /**
     * GET请求
     * @param {string} endpoint 
     * @param {object} options 
     */
    static get(endpoint, options = {}) {
        return this.request(endpoint, { ...options, method: 'GET' });
    }

    /**
     * POST请求
     * @param {string} endpoint 
     * @param {object} data 
     * @param {object} options 
     */
    static post(endpoint, data, options = {}) {
        return this.request(endpoint, {
            ...options,
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * 获取当前用户信息
     */
    static async getCurrentUser() {
        try {
            const response = await this.get('/auth/current');
            return response.data;
        } catch (error) {
            console.error('获取用户信息失败:', error);
            return null;
        }
    }

    /**
     * 检查并更新认证状态
     */
    static async checkAuth() {
        if (Auth.isAuthenticated()) {
            try {
                const userInfo = await this.getCurrentUser();
                if (userInfo) {
                    return true;
                }
                Auth.clearAuth();
                return false;
            } catch (error) {
                Auth.clearAuth();
                return false;
            }
        }
        return false;
    }
}

// 导出工具类
window.Auth = Auth;
window.ApiClient = ApiClient; 